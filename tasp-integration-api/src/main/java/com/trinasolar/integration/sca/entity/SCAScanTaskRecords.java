package com.trinasolar.integration.sca.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trinasolar.integration.api.enums.CheckStatusEnum;
import lombok.Data;

@Data
@TableName("sca_scan_task_records")
public class SCAScanTaskRecords {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long taskId;
    private CheckStatusEnum checkStatus;
}