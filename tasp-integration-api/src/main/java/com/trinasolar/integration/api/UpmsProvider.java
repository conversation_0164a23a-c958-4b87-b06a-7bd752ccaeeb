package com.trinasolar.integration.api;

import com.trinasolar.integration.api.dto.R;
import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.api.dto.UserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "harmonycloud-kepler-upms-biz", path = "/harmony/kepler/upms/u/users", url = "${upms.url:https://tasp.trinasolar.com}")
public interface UpmsProvider {

    @PostMapping("/ids")
    R<List<UserDTO>> getByIds(@RequestBody List<Long> ids);

    @GetMapping("/current")
    R<User> getCurrents(@RequestHeader("Authorization") String token);


}