package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trinasolar.integration.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tags;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;


@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tasp_base.inte_app")
@EqualsAndHashCode(callSuper = true)
public class AppSystem extends BaseEntity {

	private static final long serialVersionUID = 1L;
	
	@Schema(description = "应用中文名称")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String cnName;
	private String cnSimpleName;

	@Schema(description = "应用英文名称，唯一")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String enName;
	private String enSimpleName;

	@Schema(description = "应用观云台名称")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String cloudName;

	@Schema(description = "观云台应用访问ID")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String cloudAppId;

	@Schema(description = "观云台应用所属项目ID")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String cloudProjectId;

	@Schema(description = "观云台应用所属项目名称")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String cloudProjectName;

	@Schema(description = "观云台应用所属项目别名")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String cloudProjectAliasName;

	@Schema(description = "观云台应用所属租户ID")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String cloudTenantId;

	@Schema(description = "观云台应用所属租户名称")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String cloudTenantName;

	@Schema(description = "观云台应用所属租户别名")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String cloudTenantAliasName;

	@Schema(description = "观云台应用所属分区英文名称")
	private String cloudNamespaceId;

	@Schema(description = "观云台应用所属分区名称")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String cloudNamespaceName;

	@Schema(description = "观云台应用所属分区集群")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String cloudClusterId;

	@Schema(description = "应用状态注册、发布、..")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String stageStatus;

	@Schema(description = "应用下线标识 1： 下线 0：上线")
	@TableField(insertStrategy = FieldStrategy.NOT_NULL)
	private String lockFlag;

	@Schema(description = "应用访问地址")
	private String appUrl;

	@Schema(description = "应用备注")
	private String remark;

	@Schema(description = "详情图片url")
	private String detailPicUrl;

	@Schema(description = "logo图标url")
	private String logoUrl;

	@Schema(description="应用 title 类型：1 单行标题； 2 双行标题； 3 图片标题")
	private Integer titleType;

	@Schema(description="图片标题路径")
	private String imgTitle;

	@Schema(description="大标题")
	private String title;

	@Schema(description="小标题")
	private String subTitle;

	@Schema(description="背景图")
	private String bgImage;

	@Schema(description="显示方式： 1 自适应， 2 拉伸")
	private Integer showType;

	@Schema(description="对齐方式： 1 左对齐， 2 居中， 3 右对齐")
	private Integer alignment;

	@Schema(description = "应用UI风格CODE")
	private Long appStyle;

	@Schema(description = "应用类型")
	private String appType;

	@Schema(description="是否是外部应用")
	private Integer isOuter;

	@Schema(description="过期时间，用于判断应用是否过期，产品化推送的应用会过期，通过平台创建的应用不会过期")
	private LocalDateTime expireTime;

	private Integer urlType;

	@Schema(description="json 格式的数据， 用于控制框体应用前端按钮的显隐")
	private String configItems;

	@Schema(description="首页地址，若此值不为空，那么创建应用时会创建一个首页服务信息")
	private String homePage;

	@Schema(description="展示应用首页：0否，1是")
	private Integer showHomepage;

	@Schema(description="应用管理访问地址")
	private String manageUrl;

	@Schema(description="应用类型 1 移动端  0 web 端")
	@TableField("is_mobile")
	private Integer isMobile = 0;

	@Schema(description="同时管控pc,app菜单:1是,0否")
	@TableField("is_manage_pc_app_menu")
	private Integer isManagePcAppMenu = 0;

	@Schema(description="菜单是否收缩： 1 是， 0 否")
	private Integer contraction;

	@Schema(description="所属平台英文名")
	private String platformEnName;

	@Schema(description="所属平台中文名")
	private String platformCnName;

	@Schema(description="是否支持 ie 浏览器， 0 不支持  1 支持")
	private Integer ieSupported;

	@Schema(description="IE 提示信息富文本")
	private String ieTip;

	@Schema(description="是否控制功能权限  0 不控制  1 控制")
	private Integer isControlFP;

	@Schema(description="是否控制数据权限  0 不控制  1 控制")
	private Integer isControlDP;

	@Schema(description="是否是容器云应用")
	@TableField(value = "is_container_app")
	private Integer isContainerApp;

	@Schema(description="租户是否显示 1 显示， 0不显示")
	private Integer tenantShow;

	@Schema(description="所属业务域")
	private String businessDomain;

	@Schema(description="租户组")
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String tenantGroup;

	@Schema(description="是否账号租户切换")
	@TableField(value = "is_change_tenant")
	private Integer isChangeTenant;
	/**
	 * 应用系统类型
	 */
	private String appSysType;
	/**
	 * 应用组
	 */
	private String appGroup;
	/**
	 * 应用服务级别
	 *
	 */
	private String appServiceLevel;
	/**
	 * 建设方式
	 */
	private String constructionType;
	/**
	 * 开发语言
	 */
	private String devLanguage;
	/**
	 * 团队名称
	 */
	private String teamName;
	/**
	 * 是否创建gitlab仓库组
	 */
	private Boolean gitlab;
	/**
	 * 是否创建devops产品
	 */
	private Boolean devops;
	/**
	 * 是否创建apm应用
	 */
	private Boolean apm;

	/**
	 * 应用编码
	 */
	private String code;
	/**
	 * 访问地址
	 */
	private String accessUrl;
	/**
	 * 使用手册
	 */
	private String manualUrl;
	/**
	 * 实际上线时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime actuallyTime;
	/**
	 * 集群
	 */
	private String cluster;
	/**
	 * 关联系统
	 */
	private String relationSystem;
	/**
	 * 节点规格
	 */
	private String nodeSpec;
	/**
	 * 版本
	 */
	private String version;


	/*************** 新增字段 ****************/
	@Schema(description = "应用域，单选，数据字典维护，目前只有“MBT&DT”")
	private String appDomain;

	@Schema(description = "应用,单选，数据字典维护，与应用域联动")
	private String appName;

	@Schema(description = "产品线，下拉选择框，可选值包括AI产品组、低代码、RPA组、技术平台组 、数据产品组、数据平台组、外购业务应用产品组、外购职能应用产品组、支撑组、自研应用产品组")
	private String productLine;

	@Schema(description = "业务版块，下拉选择框，可选值包括储能、光伏、支架、研究院、解决方案、通用")
	@NotNull(message = "请输入业务版块")
	private String bizUnit;

	@Schema(description = "业务范围类型，下拉选择框，可选值：营销服、智能制造、其他领域")
	@NotNull(message = "请输入业务范围类型")
	private String bizScopeType;

	@Schema(description = "业务范围，下拉选择框，与“业务范围类型”联动")
	@NotNull(message = "请输入业务范围")
	private String bizScope;

	@Schema(description = "同步下游系统ID，多选")
	private String syncNextSystem;

	@Schema(description = "外采产品名称，当“建设方式”为“采购”时，该项必填")
	private String externalProductName;

	@Schema(description = "产品方，当“建设方式”为“采购”时，该项必填")
	private String productVendor;

	@Schema(description = "产品实施方，“建设方式”为“采购”时，默认取产品方，可修改")
	private String implVendor;

	@Schema(description = "授权方式，当“建设方式”为“采购”时必填，单选，可选值包括:永久、多年、年度")
	private String licenseMode;

	@Schema(description = "授权方式说明，对授权方式的补充说明")
	private String licenseDescription;

	@Schema(description = "重要性等级，下拉选择框，可选值：一级、二级、三级")
//    @NotNull(message = "请输入重要性等级")
	private String priorityLevel;

	@Schema(description = "服务时间，建议格式“7*24小时服务”，“5*8小时服务”")
	@NotNull(message = "请输入服务时间")
	private String serviceHours;

	@Schema(description = "系统定位描述")
	private String description;

	@Schema(description = "流程域")
	private String flowDomain;

	public boolean isMobile(){
		return this.isMobile == 1;
	}

}
