package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

/**
 * 扫描任务信息DTO
 * <AUTHOR>
 */
@Data
public class ScanTaskInfoDTO {

    @JsonAlias("task_id")
    private String taskId;

    @JsonAlias("status")
    private Integer status;

    @JsonAlias("progress")
    private Integer progress;

    @JsonAlias("start_time")
    private String startTime;

    @JsonAlias("end_time")
    private String endTime;
}