package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * SCA项目项数据传输对象
 * <AUTHOR>
 */
@Data
@Schema(description = "SCA项目项数据传输对象，用于存储和传输软件成分分析的项目信息")
public class ScaItemDTO {
    @Schema(description = "项目ID", example = "1", required = true)
    private Integer id;

    @Schema(description = "项目名称", example = "示例项目", required = true)
    private String name;

    @Schema(description = "项目级别", example = "high", required = false)
    private String level;

    @Schema(description = "检查状态", example = "completed", required = false)
    private String checkStatus;

    @Schema(description = "检查状态编号", example = "1", required = false)
    @JsonAlias("check_status_num")
    private Integer checkStatusNum;

    @Schema(description = "开始时间", example = "2023-10-01 00:00:00", required = false)
    @JsonAlias("start_time")
    private String startTime;

    @Schema(description = "结束时间", example = "2023-10-01 12:00:00", required = false)
    @JsonAlias("end_time")
    private String endTime;

    @Schema(description = "负责人", example = "张三", required = false)
    private String owner;

    @Schema(description = "负责人ID", example = "1001", required = false)
    @JsonAlias("owner_id")
    private Integer ownerId;

    @Schema(description = "项目ID", example = "2001", required = false)
    @JsonAlias("project_id")
    private Integer projectId;

    @Schema(description = "项目名称", example = "主项目", required = false)
    @JsonAlias("project_name")
    private String projectName;

    @Schema(description = "代码类型", example = "java", required = false)
    @JsonAlias("code_type")
    private String codeType;

    @Schema(description = "代码类型协议", example = "1", required = false)
    @JsonAlias("code_type_protocol")
    private Integer codeTypeProtocol;

    @Schema(description = "代码类型编号", example = "2", required = false)
    @JsonAlias("code_type_num")
    private Integer codeTypeNum;

    @Schema(description = "代码URL", example = "https://github.com/example/project", required = false)
    @JsonAlias("code_url")
    private String codeUrl;

    @Schema(description = "代码分支", example = "main", required = false)
    private String branch;

    @Schema(description = "用户名", example = "user123", required = false)
    private String username;

    @Schema(description = "扫描行数", example = "10000", required = false)
    @JsonAlias("scan_lines")
    private Integer scanLines;

    @Schema(description = "任务数量", example = "5", required = false)
    @JsonAlias("task_num")
    private Integer taskNum;

    @Schema(description = "任务名称列表", example = "[漏洞扫描,许可证检查]", required = false)
    @JsonAlias("task_names")
    private List<String> taskNames;

    @Schema(description = "任务列表", required = false)
    private List<Task> tasks;

    @Schema(description = "分数", example = "90", required = false)
    private Integer score;

    @Schema(description = "是否启用邮件通知", example = "true", required = false)
    @JsonAlias("email_enabled")
    private Boolean emailEnabled;

    @Schema(description = "邮件列表", required = false)
    private Object emails;

    @Schema(description = "成员列表", required = false)
    private List<Object> members;

    @Schema(description = "Webhook URL", example = "https://example.com/webhook", required = false)
    @JsonAlias("webhook_url")
    private String webhookUrl;

    @Schema(description = "扫描进度", example = "80", required = false)
    @JsonAlias("scan_progress")
    private Integer scanProgress;

    @Schema(description = "编程语言", example = "java", required = false)
    private Object language;

    @Schema(description = "是否自动检测语言", example = "1", required = false)
    @JsonAlias("auto_lang")
    private Integer autoLang;

    @Schema(description = "是否启用白名单过滤", example = "true", required = false)
    @JsonAlias("enable_white_filter")
    private Boolean enableWhiteFilter;

    @Schema(description = "创建时间", example = "2023-09-30 15:30:00", required = false)
    @JsonAlias("created_time")
    private String createdTime;

    @Schema(description = "组件级别统计", required = false)
    @JsonAlias("comp_level_count")
    private CompLevelCount compLevelCount;

    @Schema(description = "漏洞级别统计", required = false)
    @JsonAlias("leak_level_count")
    private LeakLevelCount leakLevelCount;

    @Schema(description = "许可证级别统计", required = false)
    @JsonAlias("license_level_count")
    private LicenseLevelCount licenseLevelCount;

    @Schema(description = "扫描模式", example = "1", required = false)
    @JsonAlias("scan_mode")
    private Integer scanMode;

    @Schema(description = "规则组", required = false)
    @JsonAlias("rules_group")
    private List<Object> rulesGroup;

    @Schema(description = "上次耗时", example = "60s", required = false)
    @JsonAlias("last_cost")
    private String lastCost;

    @Schema(description = "规则组ID", example = "101", required = false)
    @JsonAlias("rules_group_id")
    private Integer rulesGroupId;

    @Schema(description = "规则组名称", example = "默认规则组", required = false)
    @JsonAlias("rules_group_name")
    private String rulesGroupName;

    @Schema(description = "是否虚拟构建", example = "true", required = false)
    @JsonAlias("virtual_build")
    private Boolean virtualBuild;

    @Schema(description = "虚拟构建映射配置", required = false)
    @JsonAlias("virtual_build_map_config")
    private Object virtualBuildMapConfig;

    @Schema(description = "虚拟构建文件配置", required = false)
    @JsonAlias("virtual_build_file_config")
    private String virtualBuildFileConfig;

    @Schema(description = "虚拟构建系统", example = "maven", required = false)
    @JsonAlias("virtual_build_system")
    private String virtualBuildSystem;

    @Schema(description = "虚拟构建版本", example = "3.8.1", required = false)
    @JsonAlias("virtual_build_version")
    private String virtualBuildVersion;

    @Schema(description = "虚拟构建参数", required = false)
    @JsonAlias("virtual_build_params")
    private Object virtualBuildParams;

    @Schema(description = "是否启用代码块", example = "true", required = false)
    @JsonAlias("code_block_enable")
    private Boolean codeBlockEnable;

    @Schema(description = "代码块阈值", example = "80", required = false)
    @JsonAlias("code_block_threshold")
    private Integer codeBlockThreshold;

    @Schema(description = "高级模式", example = "[深度扫描,增量扫描]", required = false)
    @JsonAlias("advance_mode")
    private List<String> advanceMode;

    @Schema(description = "上传信息", required = false)
    @JsonAlias("upload_info")
    private List<UploadInfo> uploadInfo;

    @Schema(description = "命中状态", example = "1", required = false)
    @JsonAlias("hits_status")
    private Integer hitsStatus;

    @Schema(description = "是否推送报告", example = "true", required = false)
    @JsonAlias("push_report")
    private Boolean pushReport;

    @Schema(description = "推送条件", example = "1", required = false)
    @JsonAlias("push_condition")
    private Integer pushCondition;

    @Schema(description = "是否扫描所有版本", example = "false", required = false)
    @JsonAlias("scan_all_version")
    private Boolean scanAllVersion;

    @Schema(description = "是否镜像扫描", example = "false", required = false)
    @JsonAlias("image_scan")
    private Boolean imageScan;

    @Schema(description = "是否保存源代码", example = "true", required = false)
    @JsonAlias("save_source_code")
    private Boolean saveSourceCode;

    @Schema(description = "是否启用定时任务", example = "true", required = false)
    @JsonAlias("scheduled_on")
    private Boolean scheduledOn;

    @Schema(description = "定时任务类型", example = "1", required = false)
    @JsonAlias("scheduled_type")
    private Integer scheduledType;

    @Schema(description = "每周定时天数", required = false)
    @JsonAlias("scheduled_dow")
    private Object scheduledDow;

    @Schema(description = "每月定时日期", example = "15", required = false)
    @JsonAlias("scheduled_dom")
    private String scheduledDom;

    @Schema(description = "定时时间", example = "00:00:00", required = false)
    @JsonAlias("scheduled_time")
    private String scheduledTime;

    @Schema(description = "定时小时", example = "0", required = false)
    @JsonAlias("scheduled_hour")
    private Integer scheduledHour;

    @Schema(description = "定时分钟", example = "0", required = false)
    @JsonAlias("scheduled_minute")
    private Integer scheduledMinute;

    @Schema(description = "下次执行时间", example = "2023-10-02 00:00:00", required = false)
    @JsonAlias("next_schedule_time")
    private String nextScheduleTime;

    /**
     * 任务类
     */
    @Data
    @Schema(description = "任务信息")
    public static class Task {
        @Schema(description = "任务ID", example = "1001", required = true)
        private Long id;

        @Schema(description = "任务名称", example = "漏洞扫描", required = true)
        private String name;

        @Schema(description = "任务状态", example = "1", required = false)
        private Integer status;
    }

    /**
     * 组件级别统计类
     */
    @Data
    @Schema(description = "组件级别统计信息")
    public static class CompLevelCount {
        @Schema(description = "严重漏洞数量", example = "2", required = false)
        @JsonAlias("leak_serious")
        private Integer leakSerious;

        @Schema(description = "高危漏洞数量", example = "5", required = false)
        @JsonAlias("leak_high")
        private Integer leakHigh;

        @Schema(description = "中危漏洞数量", example = "10", required = false)
        @JsonAlias("leak_mid")
        private Integer leakMid;

        @Schema(description = "低危漏洞数量", example = "20", required = false)
        @JsonAlias("leak_low")
        private Integer leakLow;

        @Schema(description = "安全组件数量", example = "100", required = false)
        private Integer safe;

        @Schema(description = "无漏洞组件数量", example = "50", required = false)
        @JsonAlias("leak_none")
        private Integer leakNone;
    }

    /**
     * 漏洞级别统计类
     */
    @Data
    @Schema(description = "漏洞级别统计信息")
    public static class LeakLevelCount {
        @Schema(description = "严重漏洞数量", example = "2", required = false)
        @JsonAlias("leak_serious")
        private Integer leakSerious;

        @Schema(description = "高危漏洞数量", example = "5", required = false)
        @JsonAlias("leak_high")
        private Integer leakHigh;

        @Schema(description = "中危漏洞数量", example = "10", required = false)
        @JsonAlias("leak_mid")
        private Integer leakMid;

        @Schema(description = "低危漏洞数量", example = "20", required = false)
        @JsonAlias("leak_low")
        private Integer leakLow;

        @Schema(description = "安全项数量", example = "100", required = false)
        private Integer safe;

        @Schema(description = "无漏洞项数量", example = "50", required = false)
        @JsonAlias("leak_none")
        private Integer leakNone;
    }

    /**
     * 许可证级别统计类
     */
    @Data
    @Schema(description = "许可证级别统计信息")
    public static class LicenseLevelCount {
        @Schema(description = "严重许可证问题数量", example = "2", required = false)
        @JsonAlias("leak_serious")
        private Integer leakSerious;

        @Schema(description = "高危许可证问题数量", example = "5", required = false)
        @JsonAlias("leak_high")
        private Integer leakHigh;

        @Schema(description = "中危许可证问题数量", example = "10", required = false)
        @JsonAlias("leak_mid")
        private Integer leakMid;

        @Schema(description = "低危许可证问题数量", example = "20", required = false)
        @JsonAlias("leak_low")
        private Integer leakLow;

        @Schema(description = "安全许可证数量", example = "100", required = false)
        private Integer safe;

        @Schema(description = "无许可证问题数量", example = "50", required = false)
        @JsonAlias("leak_none")
        private Integer leakNone;
    }

    /**
     * 上传信息类
     */
    @Data
    @Schema(description = "上传信息")
    public static class UploadInfo {
        @Schema(description = "代码ID", example = "1001", required = false)
        @JsonAlias("code_id")
        private Integer codeId;

        @Schema(description = "文件名", example = "example.jar", required = false)
        @JsonAlias("file_name")
        private String fileName;
    }
}