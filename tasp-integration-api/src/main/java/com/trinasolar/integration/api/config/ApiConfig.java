package com.trinasolar.integration.api.config;


import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import feign.Util;
import feign.codec.Decoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
@Configuration
public class ApiConfig {
    // 定义全局日期格式（示例为yyyy-MM-dd HH:mm:ss）
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Bean
    public Decoder feignDecoder() {
        return (response, type) -> {
            try {
                String bodyStr = Util.toString(response.body().asReader(Util.UTF_8));
                JavaType javaType = TypeFactory.defaultInstance().constructType(type);
                JavaTimeModule javaTimeModule = new JavaTimeModule();
                // 注册LocalDateTime序列化器，指定格式
                javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT)));
                OBJECT_MAPPER.registerModule(javaTimeModule);
                return OBJECT_MAPPER.readValue(bodyStr, javaType);
            } catch (Exception e) {
                throw new RuntimeException("Error decoding response", e);
            }
        };
    }
}

