package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * API发布记录表实体类
 * 对应表: api_publish_record
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("api_publish_record")
public class ApiPublishRecord {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 发布用户id
     */
    private String userId;

    /**
     * 发布组id
     */
    private Long pubOrgId;

    /**
     * 请求实例id
     */
    private String instanceId;

    /**
     * 请求参数JSON
     */
    private String requestJson;

    /**
     * 0:申请中 1:通过 2:拒绝
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}