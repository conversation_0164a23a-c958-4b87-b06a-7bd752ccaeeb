package com.trinasolar.integration.api;

import com.trinasolar.integration.api.dto.R;
import com.trinasolar.integration.api.entity.DictItem;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(name = "remoteDictService", path = "/harmony/kepler/upms/u/", url = "${upms.url:https://tasp.trinasolar.com}")
public interface RemoteDictProvider {

    @GetMapping("/dict/list/{dictId}")
    R<List<DictItem>> listItemByDictId(@PathVariable("dictId") Long dictId);


    /**
     * 根据字典类型获取字典项
     *
     * @param parentType 父级字典类型
     * @return
     */
    @GetMapping("/dict/parentType/{parentType}")
    R<List<DictItem>> getDictItemList(@PathVariable("parentType") String parentType);

    /**
     * 根据id获取字典项详情
     *
     * @param id
     * @return
     */
    @GetMapping("/dict/detailDictItem/{id}")
    R<DictItem> detailDictItem(@PathVariable("id") Long id);
}
