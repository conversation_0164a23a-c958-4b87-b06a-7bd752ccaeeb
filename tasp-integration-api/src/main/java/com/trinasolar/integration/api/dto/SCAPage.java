package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SCAPage<T> {
    private Integer total;
    @JsonAlias("page")
    private Integer pageNo;
    @JsonAlias("per_page")
    private Integer pageSize;
    private Integer pages;
    @JsonAlias("has_pre")
    private Boolean hasPre;
    @JsonAlias("has_next")
    private Boolean hasNext;
    @JsonAlias("prev_num")
    private Integer prevNum;
    @JsonAlias("next_num")
    private Integer nextNum;
    @JsonAlias("items")
    private List<T> records;
}
