package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ScanTaskResultDTO {
    @JsonAlias("id")
    private Integer id;

    @JsonAlias("name")
    private String name;

    @JsonAlias("level")
    private String level;

    @JsonAlias("check_status")
    private String checkStatus;

    @JsonAlias("check_status_num")
    private Integer checkStatusNum;

    @JsonAlias("start_time")
    private String startTime;

    @JsonAlias("start_ts")
    private Long startTs;

    @JsonAlias("end_time")
    private String endTime;

    @JsonAlias("end_ts")
    private Long endTs;

    @JsonAlias("owner")
    private String owner;

    @JsonAlias("owner_id")
    private Integer ownerId;

    @JsonAlias("members")
    private List<Object> members;

    @JsonAlias("project_id")
    private Integer projectId;

    @JsonAlias("project_name")
    private String projectName;

    @JsonAlias("module_id")
    private Integer moduleId;

    @JsonAlias("module_name")
    private String moduleName;

    @JsonAlias("code_type")
    private String codeType;

    @JsonAlias("code_type_num")
    private Integer codeTypeNum;

    @JsonAlias("code_type_protocol")
    private Integer codeTypeProtocol;

    @JsonAlias("code_url")
    private String codeUrl;

    @JsonAlias("branch")
    private String branch;

    @JsonAlias("username")
    private String username;

    @JsonAlias("scan_lines")
    private Integer scanLines;

    @JsonAlias("score")
    private Integer score;

    @JsonAlias("email_enabled")
    private Boolean emailEnabled;

    @JsonAlias("emails")
    private List<Object> emails;

    @JsonAlias("webhook_url")
    private String webhookUrl;

    @JsonAlias("scan_progress")
    private Integer scanProgress;

    @JsonAlias("language")
    private List<Integer> language;

    @JsonAlias("auto_lang")
    private Integer autoLang;

    @JsonAlias("enable_white_filter")
    private Boolean enableWhiteFilter;

    @JsonAlias("created_time")
    private String createdTime;

    @JsonAlias("created_ts")
    private Long createdTs;

    @JsonAlias("scan_mode")
    private Integer scanMode;

    @JsonAlias("comp_level_count")
    private CompLevelCount compLevelCount;

    @JsonAlias("leak_level_count")
    private LeakLevelCount leakLevelCount;

    @JsonAlias("license_level_count")
    private LicenseLevelCount licenseLevelCount;

    @JsonAlias("rules_group")
    private List<Object> rulesGroup;

    @JsonAlias("rules_group_id")
    private Integer rulesGroupId;

    @JsonAlias("rules_group_name")
    private String rulesGroupName;

    @JsonAlias("last_cost")
    private String lastCost;

    @JsonAlias("push_report")
    private Boolean pushReport;

    @JsonAlias("push_condition")
    private Integer pushCondition;

    @JsonAlias("virtual_build")
    private Boolean virtualBuild;

    @JsonAlias("virtual_build_map_config")
    private Object virtualBuildMapConfig;

    @JsonAlias("virtual_build_file_config")
    private String virtualBuildFileConfig;

    @JsonAlias("virtual_build_system")
    private String virtualBuildSystem;

    @JsonAlias("virtual_build_version")
    private String virtualBuildVersion;

    @JsonAlias("virtual_build_params")
    private Object virtualBuildParams;

    @JsonAlias("code_block_enable")
    private Boolean codeBlockEnable;

    @JsonAlias("code_block_threshold")
    private Integer codeBlockThreshold;

    @JsonAlias("advance_mode")
    private List<Object> advanceMode;

    @JsonAlias("extra")
    private String extra;

    @JsonAlias("code_vcs_version")
    private String codeVcsVersion;

    @Data
    public static class CompLevelCount {
        @JsonAlias("safe")
        private Integer safe;

        @JsonAlias("leak_high")
        private Integer leakHigh;

        @JsonAlias("leak_mid")
        private Integer leakMid;

        @JsonAlias("leak_low")
        private Integer leakLow;

        @JsonAlias("leak_serious")
        private Integer leakSerious;
    }

    @Data
    public static class LeakLevelCount {
        @JsonAlias("safe")
        private Integer safe;

        @JsonAlias("leak_high")
        private Integer leakHigh;

        @JsonAlias("leak_mid")
        private Integer leakMid;

        @JsonAlias("leak_low")
        private Integer leakLow;

        @JsonAlias("leak_serious")
        private Integer leakSerious;
    }

    @Data
    public static class LicenseLevelCount {
        @JsonAlias("safe")
        private Integer safe;

        @JsonAlias("mid")
        private Integer mid;

        @JsonAlias("high")
        private Integer high;
    }
}