package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2025/8/7 13:43
 */
@Data
@Accessors(chain = true)
@TableName("tasp_base.sys_user_app")
public class UserApp {
    private static final long serialVersionUID = 7639770442377893304L;
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private Long userId;

    private String userCode;

    /**
     * 应用系统ID
     */
    private Long appId;

    private String markType;

    private String createTime;
}
