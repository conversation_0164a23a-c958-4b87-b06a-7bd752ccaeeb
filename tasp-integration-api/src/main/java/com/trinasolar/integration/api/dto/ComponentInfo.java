package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "组件信息实体类")
public class ComponentInfo {
    @Schema(description = "组件ID", example = "435")
    private Integer id;

    @Schema(description = "组件名称", example = "io.github.openfeign:feign-core")
    private String name;

    @Schema(description = "组件版本", example = "11.10")
    private String version;

    @Schema(description = "最新版本", example = "13.5")
    @JsonAlias("latest_version")
    private String latestVersion;

    @Schema(description = "风险等级", example = "低危")
    private String level;

    @Schema(description = "支持语言", example = "Java/Kotlin/Groovy/Scala")
    private String language;

    @Schema(description = "漏洞数量", example = "0")
    @JsonAlias("leak_num")
    private Integer leakNum;

    @Schema(description = "额外信息", example = "非最新版本，组件风险等级至少变为低危")
    private String extra;

    @Schema(description = "组件描述", example = "Feign Core")
    private String description;

    @Schema(description = "归属信息列表")
    private List<Attribution> attributions;

    @Schema(description = "风险类型列表", example = "['非最新版']")
    @JsonAlias("risk_types")
    private List<String> riskTypes;

    @Schema(description = "许可证信息列表")
    private List<License> licenses;

    @Schema(description = "搜索URL", example = "https://mvnrepository.com/artifact/io.github.openfeign/feign-core")
    @JsonAlias("search_url")
    private String searchUrl;

    @Schema(description = "GAV坐标", example = "pkg:maven/io.github.openfeign/feign-core@11.10")
    @JsonAlias("gav_coordinate")
    private String gavCoordinate;

    @Schema(description = "Jira状态", example = "0")
    @JsonAlias("jira_status")
    private Integer jiraStatus;

    @Schema(description = "禅道状态", example = "0")
    @JsonAlias("zentao_status")
    private Integer zentaoStatus;

    @Schema(description = "子仓库")
    @JsonAlias("sub_repository")
    private String subRepository;

    @Schema(description = "来源", example = "3")
    private Integer source;

    @Schema(description = "规则组")
    @JsonAlias("rules_group")
    private List<Object> rulesGroup;

    @Schema(description = "依赖类型列表", example = "[1, 0]")
    @JsonAlias("dep_type")
    private List<Integer> depType;

    @Schema(description = "依赖信息列表")
    @JsonAlias("dep_info")
    private List<DepInfo> depInfo;

    @Schema(description = "使用状态", example = "未使用")
    @JsonAlias("use_status")
    private String useStatus;

    @Schema(description = "组件引用信息")
    @JsonAlias("comp_refer")
    private CompRefer compRefer;

    @Schema(description = "扫描级别", example = "1")
    @JsonAlias("scan_level")
    private Integer scanLevel;

    @Schema(description = "间接组件列表")
    @JsonAlias("indirect_comp")
    private List<IndirectComp> indirectComp;


    @Schema(description = "校验结果", example = "符合基线|不符合基线|暂未维护")
    private String baseLineStatus;


    @Data
    @Schema(description = "归属信息")
    public static class Attribution {
        @Schema(description = "归属ID", example = "4603")
        private Integer id;

        @Schema(description = "归属名称", example = "tasp-ljh-integration")
        private String name;
    }

    @Data
    @Schema(description = "许可证信息")
    public static class License {
        @Schema(description = "许可证类型", example = "APACHE-2.0")
        private String license;

        @Schema(description = "许可证说明", example = "允许商业集成且没有开源风险 安全")
        private String explain;

        @Schema(description = "许可证级别", example = "安全")
        @JsonAlias("license_level")
        private String licenseLevel;

        @Schema(description = "许可证URL", example = "https://spdx.org/licenses/Apache-2.0.html")
        @JsonAlias("license_url")
        private String licenseUrl;

        @Schema(description = "URL列表")
        private List<String> url;

        @Schema(description = "OSI认证", example = "true")
        @JsonAlias("osi_certification")
        private Boolean osiCertification;

        @Schema(description = "FSF认证", example = "true")
        @JsonAlias("fsf_certification")
        private Boolean fsfCertification;

        @Schema(description = "许可证全名", example = "Apache License 2.0")
        private String fullname;

        @Schema(description = "SPDX ID", example = "apache-2.0")
        @JsonAlias("spdx_id")
        private String spdxId;
    }

    @Data
    @Schema(description = "依赖信息")
    public static class DepInfo {
        @Schema(description = "依赖名称", example = "直接&间接 direct reference & indirect reference")
        private String name;

        @Schema(description = "依赖值", example = "2")
        private Integer value;

        @Schema(description = "依赖计数", example = "1")
        private Integer count;
    }

    @Data
    @Schema(description = "组件引用信息")
    public static class CompRefer {
        @Schema(description = "当前引用路径列表")
        @JsonAlias("current_refer_path")
        private List<String> currentReferPath;

        @Schema(description = "根引用路径列表")
        @JsonAlias("root_refer_path")
        private List<String> rootReferPath;
    }

    @Data
    @Schema(description = "间接组件信息")
    public static class IndirectComp {
        @Schema(description = "父组件ID", example = "42068")
        @JsonAlias("parent_com_id")
        private Integer parentComId;

        @Schema(description = "父组件名称", example = "com.trinasolar.integration:tasp-integration-api 1.0.0")
        @JsonAlias("parent_name")
        private String parentName;

        @Schema(description = "引用组件路径", example = "com.trinasolar.integration:tasp-integration-api@1.0.0-->io.github.openfeign:feign-core@11.10")
        @JsonAlias("refs_com_path")
        private String refsComPath;
    }
}