package com.trinasolar.integration.api.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SubOrderPageDTO {
    // 订单ID
    private String id;
    
    // 产品ID
    private String productId;
    
    // 产品名称
    private String productName;
    
    // API版本号
    private String apiVersion;
    
    // 发布机构名称
    private String pubOrgName;
    
    // 订阅机构名称
    private String subOrgName;
    
    // 客户端密钥
    private String clientSecret;
    
    // 客户端ID
    private String clientId;
    
    // 创建时间
    private String createTime;
}
