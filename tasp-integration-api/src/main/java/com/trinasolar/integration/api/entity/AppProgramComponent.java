package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trinasolar.integration.entity.BaseEntity;
import lombok.Data;

/**
 * @className: ApplicationComponent
 * @Description: 应用程序组件表（下游系统和应用程序的关联表）
 * @author: pengshy
 * @date: 2025/8/26 09:16
 */
@TableName(value = "application_program_component")
@Data
public class AppProgramComponent extends BaseEntity {

    /**
     * 应用程序ID
     */
    private Long programId;

    /**
     * 组件类型 sca/gitlab
     */
    private String component;

    /**
     * sca模块ID/gitlab ID
     */
    private String componentId;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 应用系统ID
     */
    private Long systemId;
}
