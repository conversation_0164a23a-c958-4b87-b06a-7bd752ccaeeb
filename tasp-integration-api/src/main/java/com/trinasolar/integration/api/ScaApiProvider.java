package com.trinasolar.integration.api;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.integration.api.config.FeignSslConfig;
import com.trinasolar.integration.api.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(name = "sca-api-client", path = "/api", url = "${sca.url:https://***********}", configuration = FeignSslConfig.class)
public interface ScaApiProvider {

    @PutMapping("/v1/projects/{projectId}")
    ScaResponseDTO<Object> updateProjects(@RequestHeader Map<String, String> headerMap, @RequestBody JSONObject body, @PathVariable Long projectId);


    @GetMapping("/v1/projects/{projectId}")
    ScaResponseDTO<ScaProjectDTO> getProjectDetail(@RequestHeader Map<String, String> headerMap, @PathVariable Long projectId);


}
