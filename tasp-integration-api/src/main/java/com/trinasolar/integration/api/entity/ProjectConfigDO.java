package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trinasolar.integration.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 项目配置信息 DO
 *
 * <AUTHOR>
 */
@TableName("tasp_develop.tasp_project_config")
@KeySequence("tasp_project_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectConfigDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 配置名称
     */
    private String configKey;
    /**
     * 配置内容
     */
    private String configContent;
    /**
     * 配置名称（显示用）
     */
    private String configName;
    /**
     * 是否默认配置，1默认，默认不允许修改和删除
     */
    private Integer isDefault;
    /**
     * 配置描述
     */
    private String description;
    /**
     * 负责人
     */
    private String masterName;
    /**
     * 状态（0正常 1停用）
     *
     * 枚举 {@link TODO common_status 对应的类}
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除时间
     */
    private LocalDateTime deletedTime;

}