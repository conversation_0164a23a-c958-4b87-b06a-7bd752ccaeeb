package com.trinasolar.integration.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.integration.api.entity.InteAppSystemChange;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @className: InteAppSystemChangeMapper
 * @Description: 应用系统变更记录
 * @author: pengshy
 * @date: 2025/9/2 14:33
 */
@Mapper
public interface InteAppSystemChangeMapper extends BaseMapper<InteAppSystemChange> {
    default int insertBatch(List<InteAppSystemChange> list) {
        if (list == null || list.isEmpty()) {
            return 0;
        }
        int count = 0;
        for (InteAppSystemChange item : list) {
            this.insert(item);
            count++;
        }
        return count;
    }
}
