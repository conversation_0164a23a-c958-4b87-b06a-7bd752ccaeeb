package com.trinasolar.integration.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.integration.api.entity.UserProductFavoritePO;
import com.trinasolar.integration.api.mapper.UserProductFavoriteMapper;
import com.trinasolar.integration.api.service.UserProductFavoriteService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class UserProductFavoriteServiceImpl extends ServiceImpl<UserProductFavoriteMapper, UserProductFavoritePO> implements UserProductFavoriteService {

    @Override
    public void removeByUserAndProduct(Long userId, Long productId) {
        QueryWrapper<UserProductFavoritePO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId)
               .eq("product_id", productId);
        remove(wrapper);
    }

    @Override
    public List<UserProductFavoritePO> getFavoriteProductId(Long userId) {
        QueryWrapper<UserProductFavoritePO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        return list(wrapper);
    }
}