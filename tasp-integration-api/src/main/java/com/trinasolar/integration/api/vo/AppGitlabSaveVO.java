package com.trinasolar.integration.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AppGitlabSaveVO {

    public Long id;
    public String cnName;
    public String enName;
    public String namespace;
    public List<UserConcatDTO> businessLeader;
    public List<UserConcatDTO> technicalLeader;
    public List<UserConcatDTO> systemLeader;
    public List<UserConcatDTO> omLeader;
    public List<UserConcatDTO> adminLeader;
    public String businessDomain;

}
