package com.trinasolar.integration.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @className: ScaModelCreateReqDTO
 * @Description: TODO
 * @author: pengshy
 * @date: 2025/8/25 21:26
 */
@Data
public class ScaModelCreateReqDTO {
    // 是否自动识别项目语言(0:否,1:是)，自动识别语言时，language为[]
    @JSONField(name = "auto_lang")
    private Integer autoLang;

    // 1.7.0 添加黑名单（补充参数）
    // private BlacklistFiles blacklistFiles;

    // VCS分支，使用VCS方式时候选填，默认主分支
    private String branch;

    // 构建包检测是否启用（true:开启，false:关闭）
    private Boolean buildCheckEnable;

    // 是否开启代码片段检测(true:开启, false:关闭)
    private Boolean codeBlockEnable;

    // 代码片段相似度阈值(0~15)
    private Integer codeBlockThreshold;

    // 代码获取方式(0:GIT(HTTPS); 1:GIT(SSH); 2:SVN; 3:上传代码; 4.TFS; 5:Mercurial(HTTPS); 6:Mercurial(SSH); 10:BinScan)
    @JSONField(name = "code_type_num")
    private Integer codeTypeNum;

    // 代码获取协议(1:HTTPS,2:SSH)
    @JSONField(name = "code_type_protocol")
    private Integer codeTypeProtocol;

    // 是否发送邮件，选填
    @JSONField(name = "email_enabled")
    private Boolean emailEnabled;

    // 用户的邮箱，选填
    private List<String> emails;

    // 白名单过滤，选填
    private Boolean enableWhiteFilter;

    // 语言类型(1:JAVA, 2:C/C++, 4:C#, 5:ANDROID, 6:Object-C, 7:PHP, 8:JAVASCRIPT, 9:PYTHON, 10:GOLANG, 11:Swift, 12:Ruby ,13:Rust)
    private List<Integer> language;

    // 模块名称，必填
    @JSONField(name = "name")
    private String name;

    // 系统类型 1-package 2-RTOS (3-bin 4-hex 5-s19) 10-离线 1.8.0添加
    @JSONField(name = "os_type")
    private Integer osType;

    // VCS密码，使用VCS方式时候选填
    private String passwd;

    // 代码路径或VCS URL，使用VCS方式时候必填
    private String path;

    // 所属项目ID，必填
    @NotNull(message = "所属项目ID不能为空")
    @JSONField(name = "project_id")
    private Integer projectId;

    // 推送条件，选填(0全部，1检出低危，2检出中危，检出高危)
    @JSONField(name = "push_condition")
    private Integer pushCondition;

    // 报告推送是否勾选，选填
    @JSONField(name = "push_report")
    private Boolean pushReport;

    // 组件真实性引用 1.7.1添加
    @JSONField(name = "real_comp_enable")
    private Boolean realCompEnable;

    // 漏洞真实性引用 1.7.1添加
    @JSONField(name = "real_vuln_enable")
    private Boolean realVulnEnable;

    // 私库配置 Java
    //  private List<RemoteRepository> remoteRepository;

    // 自定义策略组id，选填
    @JSONField(name = "rules_group_id")
    private Integer rulesGroupId;

    // 1.3.1-ADD-是否保存源代码，true-保留，false-删除
    @JSONField(name = "save_source_code")
    private Boolean saveSourceCode;

    // 是否扫描全版本(默认：否)
    @JSONField(name = "scan_all_version")
    private Boolean scanAllVersion;

    // ssh 密钥名称
    @JSONField(name = "ssh_key")
    private String sshKey;

    // gitlab获取代码，根据access_token拉取代码
    @JSONField(name = "token")
    private String token;

    // 代码上传使用，代码上传不使用Path字段, code_id：上传的代码的id，代码上传方式时必填
    private List<UploadInfo> uploadInfo;

    // VCS用户名，使用VCS方式时候选填
    private String username;

    // 是否模拟构建(false不使用，true使用)
    @JSONField(name = "virtual_build")
    private Boolean virtualBuild;

    // 模拟构建config
    @JSONField(name = "virtual_build_config")
    private Object virtualBuildConfig;

    // 构建参数
    @JSONField(name = "virtual_build_params")
    private Object virtualBuildParams;

    // 构建系统
    @JSONField(name = "virtual_build_system")
    private String virtualBuildSystem;

    // 构建版本
    @JSONField(name = "virtual_build_version")
    private String virtualBuildVersion;

    // 是否开启webhook，选填 1.3.1-Modify
    @JSONField(name = "webhook_enabled")
    private Boolean webhookEnable;

    // webhook地址，选填 1.3.1-Modify 改为webhook id list
    private List<Integer> webhooks;

    /**
     * 黑名单文件内部类
     */
    @Data
    public static class BlacklistFiles {
        private List<String> prefixMatchFiles;
        private List<String> regexpMatchFiles;
        private List<String> suffixMatchFiles;
    }

    /**
     * 上传信息内部类
     */
    @Data
    public static class UploadInfo {
        private Integer codeId;
        private String fileName;
    }
}
