package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class Event {
    // API标识
    @JsonAlias("api_id")
    private String apiId;
    // API名称
    @JsonAlias("api_name")
    private String apiName;
    // API版本
    @JsonAlias("api_version")
    private String apiVersion;
    // 请求时间
    @JsonAlias("datetime")
    private String datetime;
    // 请求头
    @JsonAlias("request_http_headers")
    private List<Map<String, String>> requestHttpHeaders;
    // 开发者组织ID
    @JsonAlias("developer_org_id")
    private String developerOrgId;
    // 开发者组织名称
    @JsonAlias("developer_org_name")
    private String developerOrgName;
    // 开发者组织标题
    @JsonAlias("developer_org_title")
    private String developerOrgTitle;
    // 产品ID
    @JsonAlias("product_id")
    private String productId;
    // 产品名称
    @JsonAlias("product_name")
    private String productName;
    // 产品标题
    @JsonAlias("product_title")
    private String productTitle;
    // 产品版本
    @JsonAlias("product_version")
    private String productVersion;
    // 查询参数
    @JsonAlias("query_string")
    private String queryString;
    // 请求体
    @JsonAlias("request_body")
    private String requestBody;

    @JsonAlias("response_body")
    private String responseBody;

    @JsonAlias("request_method")
    private String requestMethod;

    // 状态码
    @JsonAlias("status_code")
    private String statusCode;
    // 请求耗时
    @JsonAlias("time_to_serve_request")
    private String timeToServeRequest;
    // 请求路径
    @JsonAlias("uri_path")
    private String uriPath;
    // 网关URL
    @JsonAlias("show_gw_url")
    private String showGwUrl;
    // 处理网关名称
    @JsonAlias("show_gw_name")
    private String showGwName;

    // 处理网关IP
    @JsonAlias("gateway_ip")
    private String gatewayIp;

    // 处理网关
    @JsonAlias("gateway_service_name")
    private String gatewayServiceName;
}
