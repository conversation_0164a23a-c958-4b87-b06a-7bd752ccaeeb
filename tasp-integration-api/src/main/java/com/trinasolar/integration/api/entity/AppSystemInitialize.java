package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trinasolar.integration.entity.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@TableName("tasp_base.inte_app_initialize")
public class AppSystemInitialize extends BaseEntity {
    /**
     * 应用系统ID
     */
    public Long appId;
    /**
     * 关联系统ID
     */
    public String relationId;

    /**
     * 关联系统类型
     */
    public String relationType;
    /**
     * 关联系统中-对应应用系统名称
     */
    public String relationName;

    /**
     * devops、git等关联系统目前是api创建，该字段为api响应原始数据
     */
    public String originData;

    public AppSystemInitialize(Long appId, String relationId, String relationName, String relationType, String originData) {
        this.appId = appId;
        this.relationId = relationId;
        this.relationName = relationName;
        this.relationType = relationType;
        this.originData = originData;
    }
}
