package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trinasolar.integration.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 字典项
 * </p>
 *
 * <AUTHOR>
 * @create 2019-05-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("sys_dict_item")
public class DictItem extends BaseEntity {

    private static final long serialVersionUID = -4183872786049813765L;

    /**
     * 父id
     */
    private Long parentId;

    @Schema(description= "字典ID")
    private Long dictId;

    @Schema(description= "数据值")
    private String value;

    @Schema(description= "标签名")
    private String label;

    @Schema(description= "类型")
    private String type;

    @Schema(description= "排序号")
    private Integer sort;

    @Schema(description= "描述")
    private String description;

    @TableField(value = "is_editable")
    private Integer editable;

    @TableField(value = "is_deletable")
    private Integer deletable;
}
