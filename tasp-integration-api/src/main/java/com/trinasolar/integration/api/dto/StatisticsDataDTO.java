package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

/**
 * 统计数据DTO
 * <AUTHOR>
 */
@Data
public class StatisticsDataDTO {
    @JsonAlias("high_risk_count")
    private Integer highRiskCount;

    @JsonAlias("medium_risk_count")
    private Integer mediumRiskCount;

    @JsonAlias("low_risk_count")
    private Integer lowRiskCount;

    @JsonAlias("total_risk_count")
    private Integer totalRiskCount;
}