package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.trinasolar.integration.BaseDO;
import lombok.*;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;

/**
 * 系统管理 DO
 *
 * <AUTHOR>
 */
@TableName("tasp_develop.tasc_app")
@KeySequence("tasc_app_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 系统名称
     */
    private String name;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 系统编码
     */
    private String code;
    /**
     * 应用初始化配置信息
     */
    private String appInitConfig;
    /**
     * 系统描述
     */
    private String description;
    /**
     * 系统类型
     */
    private Integer type;
    /**
     * 系统初始化步骤
     */
    private Integer appInitStep;
    /**
     * 系统初始化Git
     */
    private Integer appInitGit;
    /**
     * 负责人
     */
    private String masterName;
    /**
     * 状态（0正常 1停用）
     *
     * 枚举 {@link }
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除时间
     */
    private LocalDateTime deletedTime;

    /**
     * 应用初始化类型（1基于模板 2基于导入）
     */
    private Integer appInitType;

    /**
     * 是否完成应用初始化（0否 1是）
     */
    private Integer completeInit;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    @TableField(fill = FieldFill.INSERT, jdbcType = JdbcType.VARCHAR)
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, jdbcType = JdbcType.VARCHAR)
    private String updater;
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;

}