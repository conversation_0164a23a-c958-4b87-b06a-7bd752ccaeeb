package com.trinasolar.integration.api.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户联系方式简单实体
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2020/6/14 09:53
 */
@Data
public class UserConcatDTO implements Serializable {
    private static final long serialVersionUID = -4575586341991406601L;

    /**
     * 用户 ID
     */
    private Long id;

    /**
     * 用户账号
     */
    private String username;

    /**
     * 统一身份认证账号
     */
    private String unityAccount;

    /**
     * 用户真实姓名
     */
    private String userRealname;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 用户邮箱地址
     */
    private String email;

    private String userCode;
}
