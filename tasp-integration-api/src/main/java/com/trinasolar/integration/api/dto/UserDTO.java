package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2019/5/1
 */
@Data
public class UserDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long organizationId;

    private String userCode;

    private String username;

    private String unityAccount;

    private String userRealname;

    private String avatar;

    private String phone;

    private String email;

    private String sex;

    private String idNumber;

    private String workMobile;

    private String homePhone;

    private String homeAddress;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime entryTime;

    private String staffType;

    private String clevel;

    private String plevel;

    private String plevelName;

    private String staffTypeValue;

    private String clevelValue;


    private String plevelValue;


    private String plevelNameValue;


    private String inServiceStatusValue;


    private String appAssistantMark;


    private Long secondOrgId;

    private Long temporarilyTransferOrgId;


    private String partTimeOrgIds;


    private String admin;


    private String syncFlag;


    private String inServiceStatus;

    private Long sort;

    private String convinceGroup;

    private String description;

    private Integer locked;

    private String serialNumber;

    private Long tenantId;

    private String tenantCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    private Long creatorId;

    private Long updaterId;

    private String cloudRoleFlag;

    private String appRoleFlag;

    private String appDataRoleFlag;

    private Long orgId;

    private String orgCode;

    private String orgName;

    private String fullPathOrgName;

    private String tenantGroup;

    private Long oneOrgId;

    private Long twoOrgId;


    private Long threeOrgId;
}
