package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 开源组件基线登记表实体类
 *
 * <AUTHOR> @date
 */
@Schema(description = "开源组件基线登记表")
@Data
@TableName("register_open_source_component") // 数据库表映射
public class OpenSourceComptBaselineRegister {

    /**
     * 唯一标识（主键）
     */
    @Schema(description = "唯一标识（主键）", example = "1")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 应用程序ID
     */
    @Schema(description = "应用程序ID", required = true, example = "1001")
    private Long appId;

    /**
     * 组件名称
     */
    @Schema(description = "组件名称", required = true, example = "Spring Boot")
    private String name;

    /**
     * 基准版本
     */
    @Schema(description = "基准版本", required = true, example = "2.7.0")
    private String version;

    /**
     * 使用版本
     */
    @Schema(description = "使用版本", required = true, example = "2.7.5")
    private String useVersion;

    /**
     * 是否为基线组件 0不是|1是
     */
    @Schema(description = "是否为基线组件 0不是|1是", required = true, example = "1")
    private Integer isBaseline;

    /**
     * 是否启用 0关闭|1启用
     */
    @Schema(description = "是否启用 0关闭|1启用", required = true, example = "1")
    private Integer isUsed;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "企业级应用开发框架")
    private String description;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1001")
    private Long createBy;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1001")
    private Long updateBy;

    /**
     * 删除标识 0未删除|1已删除
     */
    @TableLogic
    @Schema(description = "删除标识 0未删除|1已删除", example = "0")
    private Integer delFlag;
}
