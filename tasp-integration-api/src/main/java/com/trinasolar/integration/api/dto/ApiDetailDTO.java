package com.trinasolar.integration.api.dto;

import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ApiDetailDTO {
    /**
     * Api编码
     */
    private String code;

    /**
     * API id
     */
    private Long id;

    /**
     * API名称
     */
    private String name;

    /**
     * 发布组Code编码
     */
    private String pubOrgCode;

    /**
     * 发布组id
     */
    private Long pubOrgId;

    /**
     * 发布组名称
     */
    private String pubOrgName;

    /**
     * 分类id
     */
    private Long tagId;

    /**
     * 分类名称
     */
    private String tagName;

    /**
     * 版本号
     */
    private String version;

    /**
     * Json数据
     */
    private String apiSpec;

    /**
     * 调试网关地址
     */
    private List<String> gatewayUrl;

    /**
     * TSL-ClientID
     */
    private String sandboxClientId;

    /**
     * TSL-ClientSecret
     */
    private String sandboxClientSecret;

    /**
     * 订阅组
     */
    private String subOrgName;


}
