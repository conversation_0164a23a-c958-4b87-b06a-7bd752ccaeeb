package com.trinasolar.integration.api;

import com.trinasolar.integration.api.vo.AppSystemVO;
import com.trinasolar.integration.api.vo.DevOpsProjectRespVO;
import com.trinasolar.integration.api.vo.PipelineRespVO;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import com.trinasolar.tasc.framework.common.pojo.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@FeignClient(name = "kepler-integration", path = "/kepler/integration",contextId = "kepler-integration-devops",url = "${integration.url}")
public interface RemoteDevopsClient {

    /**
     * 创建Devops产品
     *
     * @param appSystemVO
     * @return
     */
    @PostMapping("/devops/create/product")
    CommonResult<DevOpsProjectRespVO> createProject(@Valid @RequestBody AppSystemVO appSystemVO);

    /** 获取流水线列表
     * @param programId
     * @return
     */
    @GetMapping("/devops/pipelines")
    CommonResult<PageResult<PipelineRespVO>> getPipelines(@RequestParam(value = "programId")  Long programId);
}
