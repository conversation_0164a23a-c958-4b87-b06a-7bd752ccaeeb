package com.trinasolar.integration.api;

import com.trinasolar.integration.api.dto.ApiApproveDTO;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 */
@FeignClient(name = "harmonycloud-kepler-activiti-biz",path = "/harmony/kepler/act",url = "${upms.url:https://tasp.trinasolar.com}")
public interface ApproveProvider {

    @PostMapping("/flow/createInstance")
    CommonResult<String> createInstance(@RequestHeader("Authorization") String token, @RequestBody ApiApproveDTO apiApproveDTO);

}