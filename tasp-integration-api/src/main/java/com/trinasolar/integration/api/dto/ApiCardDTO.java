package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ApiCardDTO {
    /**
     * 发布组id
     */
    private Long id;
    /**
     * API名字
     */
    private String name;
    /**
     * API编码
     */
    private String code;
    /**
     * API版本
     */
    private String version;
    /**
     * 标签id
     */
    private Long tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 发布组名称
     */
    private String pubOrgName;

    /**
     * 发布组id
     */
    private Long pubOrgId;


    /**
     *  服务名称
     */
    @JsonAlias("description")
    private String desc;

}
