package com.trinasolar.integration.api.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Getter
public enum ApiRollbackStatus {
    PENDING("PENDING", "待审批"),
    APPROVE("APPROVE", "已通过"),
    REJECTED("REJECTED", "已驳回"),
    TERMINATED("TERMINATED", "已终止"),
    NOT_FOUND("NOT_FOUND", "未知");

    private final String code;
    private final String description;

    ApiRollbackStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}