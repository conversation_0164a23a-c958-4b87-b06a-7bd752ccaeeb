package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

/**
 * SCA响应DTO
 *
 * <AUTHOR>
 */
@Data
public class ScaResponseDTO<T> {
    @JsonAlias("ok")
    private Boolean ok;

    @JsonAlias("code")
    private Integer code;

    @JsonAlias("message")
    private String message;

    @JsonAlias("data")
    private T data;

    @JsonAlias("error")
    private String error;

    public Boolean isSuccess() {
        return Boolean.TRUE.equals(ok);
    }

    public Boolean isError() {
        return !isSuccess();
    }
}