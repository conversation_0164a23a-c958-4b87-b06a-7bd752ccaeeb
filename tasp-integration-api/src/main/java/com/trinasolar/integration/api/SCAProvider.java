package com.trinasolar.integration.api;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.integration.api.config.FeignSslConfig;
import com.trinasolar.integration.api.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(name = "sca-client", path = "/openapi", url = "${sca.url:https://172.22.4.10}", configuration = FeignSslConfig.class)
public interface SCAProvider {

    @GetMapping("/v1/modules")
    ScaResponseDTO<ScaDataDTO> getModules(@RequestHeader Map<String, String> headerMap, @RequestParam("project_id") Long projectId, @RequestParam("page") Integer page, @RequestParam("num") Integer num, @RequestParam("code_type_num") Integer codeType, @RequestParam("safe_level") Integer safeLevel, @RequestParam("scan_status") Integer scanStatus, @RequestParam("order_by_created_time") Boolean orderByCreatedTime, @RequestParam("order_by_latest_scan_time") Boolean orderByLatestScanTime);

    @GetMapping("/v1/tasks/{taskId}")
    ScaResponseDTO<ScaTaskDetailDTO> getTaskDetails(@RequestHeader Map<String, String> headerMap, @PathVariable Long taskId);

    /**
     * 获取扫描组件
     *
     * @param headerMap
     * @param taskId
     * @param pageNo
     * @param pageSize
     * @param pattern
     * @param source
     * @return
     */
    @GetMapping("/v1/tasks/{task_id}/comps")
    ScaResponseDTO<SCAPage<ComponentInfo>> getCompsByTaskId(@RequestHeader Map<String, String> headerMap
            , @PathVariable("task_id") Long taskId
            , @RequestParam("page") Integer pageNo, @RequestParam("num") Integer pageSize
            , @RequestParam(required = false) String pattern, @RequestParam(required = false) Integer source
            , @RequestParam("dep_type") Integer depType);


    @PostMapping("/v1/projects")
    ScaResponseDTO<Object> createProjects(@RequestHeader Map<String, String> headerMap, @RequestBody JSONObject body);

    @PutMapping("/v1/projects")
    ScaResponseDTO<Object> updateProjects(@RequestHeader Map<String, String> headerMap, @RequestBody JSONObject body);

    @DeleteMapping("/v1/projects")
    ScaResponseDTO<Object> deleteProjects(@RequestHeader Map<String, String> headerMap, @RequestBody JSONObject body);

    @GetMapping("/v1/projects/{projectId}")
    ScaResponseDTO<ScaProjectDTO> getProjectDetail(@RequestHeader Map<String, String> headerMap, @PathVariable Long projectId);


    @PostMapping("/v1/modules/add")
    ScaResponseDTO<Object> createModel(@RequestHeader Map<String, String> headerMap, @RequestBody JSONObject body);


    @DeleteMapping("/v1/modules")
    ScaResponseDTO<Object> deleteModel(@RequestHeader Map<String, String> headerMap, @RequestBody JSONObject body);

    @GetMapping("/v1/users")
    ScaResponseDTO<SCAPage<ScaUserRespDTO>> getAllUser(@RequestHeader Map<String, String> headerMap);
    /**
     * 获取扫描结果
     *
     * @param headerMap
     * @param taskId
     * @return
     */
    @GetMapping("v1/tasks/{task_id}")
    ScaResponseDTO<ScanTaskResultDTO> getResultByTaskId(@RequestHeader Map<String, String> headerMap, @PathVariable("task_id") Long taskId);


    /**
     * 获取扫描结果
     *
     * @param headerMap
     * @param jsonObject
     * @return
     */
    @PostMapping("v1/modules/scan")
    ScaResponseDTO<ScanTaskDTO> scan(@RequestHeader Map<String, String> headerMap, @RequestBody JSONObject jsonObject);


    @GetMapping("v1/tasks/{task_id}/scan-progress")
    ScaResponseDTO<ScanTaskProgressDTO> scanProgress(@RequestHeader Map<String, String> headerMap, @PathVariable("task_id") Long taskId);

}
