package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * SCA任务详情DTO
 */
@Data
@Schema(description = "SCA任务详情数据传输对象")
public class ScaTaskDetailDTO {
    @Schema(description = "任务ID", example = "20038")
    private Long id;

    @Schema(description = "任务名称", example = "tasp-ljh-integration-2025081915172325")
    private String name;

    @Schema(description = "风险级别", example = "严重")
    private String level;

    @Schema(description = "检查状态", example = "扫描完成")
    @JsonAlias("check_status")
    private String checkStatus;

    @Schema(description = "检查状态编号", example = "3")
    @JsonAlias("check_status_num")
    private Integer checkStatusNum;

    @Schema(description = "开始时间", example = "2025-08-19 15:17:28")
    @JsonAlias("start_time")
    private String startTime;

    @Schema(description = "开始时间戳", example = "1755587848")
    @JsonAlias("start_ts")
    private Long startTs;

    @Schema(description = "结束时间", example = "2025-08-19 15:17:49")
    @JsonAlias("end_time")
    private String endTime;

    @Schema(description = "结束时间戳", example = "1755587869")
    @JsonAlias("end_ts")
    private Long endTs;

    @Schema(description = "负责人", example = "IT_Outsource_CZ1756(李俊豪)")
    private String owner;

    @Schema(description = "负责人ID", example = "37567")
    @JsonAlias("owner_id")
    private Long ownerId;

    @Schema(description = "成员列表")
    private List<Object> members;

    @Schema(description = "项目ID", example = "286")
    @JsonAlias("project_id")
    private Long projectId;

    @Schema(description = "项目名称", example = "tasp-ljh")
    @JsonAlias("project_name")
    private String projectName;

    @Schema(description = "模块ID", example = "4603")
    @JsonAlias("module_id")
    private Long moduleId;

    @Schema(description = "模块名称", example = "tasp-ljh-integration")
    @JsonAlias("module_name")
    private String moduleName;

    @Schema(description = "代码类型", example = "GIT(HTTPS)")
    @JsonAlias("code_type")
    private String codeType;

    @Schema(description = "代码类型编号", example = "0")
    @JsonAlias("code_type_num")
    private Integer codeTypeNum;

    @Schema(description = "代码类型协议", example = "1")
    @JsonAlias("code_type_protocol")
    private Integer codeTypeProtocol;

    @Schema(description = "代码URL", example = "https://code.trinasolar.com/ipd/tasp/kepler-integration.git")
    @JsonAlias("code_url")
    private String codeUrl;

    @Schema(description = "分支", example = "develop")
    private String branch;

    @Schema(description = "用户名", example = "<EMAIL>")
    private String username;

    @Schema(description = "扫描行数", example = "0")
    @JsonAlias("scan_lines")
    private Integer scanLines;

    @Schema(description = "分数", example = "0")
    private Integer score;

    @Schema(description = "是否启用邮件通知", example = "false")
    @JsonAlias("email_enabled")
    private Boolean emailEnabled;

    @Schema(description = "邮件列表")
    private List<String> emails;

    @Schema(description = "Webhook URL")
    @JsonAlias("webhook_url")
    private String webhookUrl;

    @Schema(description = "扫描进度", example = "100")
    @JsonAlias("scan_progress")
    private Integer scanProgress;

    @Schema(description = "语言")
    private List<Integer> language;

    @Schema(description = "自动语言检测", example = "1")
    @JsonAlias("auto_lang")
    private Integer autoLang;

    @Schema(description = "是否启用白名单过滤", example = "false")
    @JsonAlias("enable_white_filter")
    private Boolean enableWhiteFilter;

    @Schema(description = "创建时间", example = "2025-08-19 15:17:23")
    @JsonAlias("created_time")
    private String createdTime;

    @Schema(description = "创建时间戳", example = "0")
    @JsonAlias("created_ts")
    private Long createdTs;

    @Schema(description = "扫描模式", example = "2")
    @JsonAlias("scan_mode")
    private Integer scanMode;

    @Schema(description = "组件级别统计")
    @JsonAlias("comp_level_count")
    private CompLevelCount compLevelCount;

    @Schema(description = "漏洞级别统计")
    @JsonAlias("leak_level_count")
    private LeakLevelCount leakLevelCount;

    @Schema(description = "许可证级别统计")
    @JsonAlias("license_level_count")
    private LicenseLevelCount licenseLevelCount;

    @Schema(description = "规则组")
    @JsonAlias("rules_group")
    private List<Object> rulesGroup;

    @Schema(description = "规则组ID", example = "0")
    @JsonAlias("rules_group_id")
    private Integer rulesGroupId;

    @Schema(description = "规则组名称")
    @JsonAlias("rules_group_name")
    private String rulesGroupName;

    @Schema(description = "最后耗时", example = "00:00:21")
    @JsonAlias("last_cost")
    private String lastCost;

    @Schema(description = "是否推送报告", example = "false")
    @JsonAlias("push_report")
    private Boolean pushReport;

    @Schema(description = "推送条件", example = "0")
    @JsonAlias("push_condition")
    private Integer pushCondition;

    @Schema(description = "是否虚拟构建", example = "false")
    @JsonAlias("virtual_build")
    private Boolean virtualBuild;

    @Schema(description = "虚拟构建映射配置")
    @JsonAlias("virtual_build_map_config")
    private Object virtualBuildMapConfig;

    @Schema(description = "虚拟构建文件配置")
    @JsonAlias("virtual_build_file_config")
    private String virtualBuildFileConfig;

    @Schema(description = "虚拟构建系统", example = "VirtualBuildType(0)")
    @JsonAlias("virtual_build_system")
    private String virtualBuildSystem;

    @Schema(description = "虚拟构建版本")
    @JsonAlias("virtual_build_version")
    private String virtualBuildVersion;

    @Schema(description = "虚拟构建参数")
    @JsonAlias("virtual_build_params")
    private Object virtualBuildParams;

    @Schema(description = "是否启用代码块", example = "false")
    @JsonAlias("code_block_enable")
    private Boolean codeBlockEnable;

    @Schema(description = "代码块阈值", example = "0")
    @JsonAlias("code_block_threshold")
    private Integer codeBlockThreshold;

    @Schema(description = "高级模式")
    @JsonAlias("advance_mode")
    private List<Object> advanceMode;

    @Schema(description = "额外信息")
    private String extra;

    @Schema(description = "代码VCS版本", example = "4b86f4266c010f319a9a78b81513ebe201202b28")
    @JsonAlias("code_vcs_version")
    private String codeVcsVersion;

    /**
     * 任务类
     */
    @Data
    @Schema(description = "任务信息")
    public static class Task {
        @Schema(description = "任务ID", example = "1001", required = true)
        private Long id;

        @Schema(description = "任务名称", example = "漏洞扫描", required = true)
        private String name;

        @Schema(description = "任务状态", example = "1", required = false)
        private Integer status;
    }

    /**
     * 组件级别统计类
     */
    @Data
    @Schema(description = "组件级别统计信息")
    public static class CompLevelCount {
        @Schema(description = "严重漏洞数量", example = "2", required = false)
        @JsonAlias("leak_serious")
        private Integer leakSerious;

        @Schema(description = "高危漏洞数量", example = "5", required = false)
        @JsonAlias("leak_high")
        private Integer leakHigh;

        @Schema(description = "中危漏洞数量", example = "10", required = false)
        @JsonAlias("leak_mid")
        private Integer leakMid;

        @Schema(description = "低危漏洞数量", example = "20", required = false)
        @JsonAlias("leak_low")
        private Integer leakLow;

        @Schema(description = "安全组件数量", example = "100", required = false)
        private Integer safe;

        @Schema(description = "无漏洞组件数量", example = "50", required = false)
        @JsonAlias("leak_none")
        private Integer leakNone;
    }

    /**
     * 漏洞级别统计类
     */
    @Data
    @Schema(description = "漏洞级别统计信息")
    public static class LeakLevelCount {
        @Schema(description = "严重漏洞数量", example = "2", required = false)
        @JsonAlias("leak_serious")
        private Integer leakSerious;

        @Schema(description = "高危漏洞数量", example = "5", required = false)
        @JsonAlias("leak_high")
        private Integer leakHigh;

        @Schema(description = "中危漏洞数量", example = "10", required = false)
        @JsonAlias("leak_mid")
        private Integer leakMid;

        @Schema(description = "低危漏洞数量", example = "20", required = false)
        @JsonAlias("leak_low")
        private Integer leakLow;

        @Schema(description = "安全项数量", example = "100", required = false)
        private Integer safe;

        @Schema(description = "无漏洞项数量", example = "50", required = false)
        @JsonAlias("leak_none")
        private Integer leakNone;
    }

    /**
     * 许可证级别统计类
     */
    @Data
    @Schema(description = "许可证级别统计信息")
    public static class LicenseLevelCount {
        @Schema(description = "严重许可证问题数量", example = "2", required = false)
        @JsonAlias("leak_serious")
        private Integer leakSerious;

        @Schema(description = "高危许可证问题数量", example = "5", required = false)
        @JsonAlias("leak_high")
        private Integer leakHigh;

        @Schema(description = "中危许可证问题数量", example = "10", required = false)
        @JsonAlias("leak_mid")
        private Integer leakMid;

        @Schema(description = "低危许可证问题数量", example = "20", required = false)
        @JsonAlias("leak_low")
        private Integer leakLow;

        @Schema(description = "安全许可证数量", example = "100", required = false)
        private Integer safe;

        @Schema(description = "无许可证问题数量", example = "50", required = false)
        @JsonAlias("leak_none")
        private Integer leakNone;
    }


}