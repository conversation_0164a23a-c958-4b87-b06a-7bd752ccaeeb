package com.trinasolar.integration.api.dto;

import lombok.Data;

import java.util.List;

/**
 * 用户信息DTO
 * 
 * <AUTHOR>
 */
@Data
public class User {
    /** 访问令牌 */
    private String accessToken;
    /** 刷新令牌 */
    private String refreshToken;
    /** 访问令牌过期时间(秒) */
    private Integer tokenExpiredSeconds;
    /** 刷新令牌过期时间(秒) */
    private Integer refreshTokenExpiredSeconds;
    /** 登录IP */
    private String loginIp;
    /** 是否移动端登录 */
    private Boolean mobile;
    /** HA访问令牌 */
    private String haAccessToken;
    /** 应用ID */
    private String appId;
    /** 应用编码 */
    private String appCode;
    /** 用户ID */
    private String id;
    /** 用户名 */
    private String username;
    /** 密码(加密) */
    private String password;
    /** 用户真实姓名 */
    private String userRealname;
    /** 邮箱 */
    private String email;
    /** 手机号 */
    private String phone;
    /** 是否锁定(0-未锁定,1-已锁定) */
    private Integer locked;
    /** 序列号 */
    private String serialNumber;
    /** 是否管理员 */
    private Boolean admin;
    /** 租户ID */
    private String tenantId;
    /** 租户编码 */
    private String tenantCode;
    /** 租户名称 */
    private String tenantName;
    /** 组织ID */
    private String orgId;
    /** 组织编码 */
    private String orgCode;
    /** 组织名称 */
    private String orgName;
    /** 二级产品组织ID */
    private String secondLevelProdOrgId;
    /** 二级产品组织编码 */
    private String secondLevelProdOrgCode;
    /** 二级产品组织名称 */
    private String secondLevelProdOrgName;
    /** 同步标记 */
    private String syncFlag;
    /** 岗位列表 */
    private List<String> posts;
    /** 应用ID列表 */
    private List<String> appIds;
    /** 角色ID列表 */
    private List<String> roleIds;
    /** 管理组织ID列表 */
    private List<String> manageOrgIds;
    /** 用户规则映射 */
    private Object userRuleMap;
    /**
     * 用户列规则映射
     */
    private Object userColumnRuleMap;
}
