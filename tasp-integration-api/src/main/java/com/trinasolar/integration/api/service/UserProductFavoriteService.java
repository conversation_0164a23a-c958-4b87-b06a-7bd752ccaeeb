package com.trinasolar.integration.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.integration.api.entity.UserProductFavoritePO;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserProductFavoriteService extends IService<UserProductFavoritePO> {
    void removeByUserAndProduct(Long userId, Long productId);
    List<UserProductFavoritePO> getFavoriteProductId(Long userId);
}