package com.trinasolar.integration.api.resq;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021-04-27 10:27
 */
@Getter
@AllArgsConstructor
public enum ResultEnum {
    SUCCESS(0, "处理成功"),
    FAILURE(1, "处理失败"),

    // 系统级别异常码
    SERVER_ERROR(-1, "服务异常"),
    BUSINESS_ERROR(5000, "业务异常"),
    IN_SERVICE_ERROR(5003, "内部服务调用异常"),
    PARAM_ERROR(4000, "参数异常"),
    AUTH_ERROR(4001, "登录异常"),
    FORBIDDEN_ERROR(4003, "权限异常"),
    NOT_FOUND_ERROR(4004, "服务未找到"),

    WEB_SAVE_ERROR(22001,"增加失败"),
    WEB_IS_EXIST(22002,"微前端已存在"),

    /**
     * 用户未登录，请登陆后进行访问
     */
    USER_NEED_LOGIN(11001, "用户未登录，请登陆后进行访问"),
    /**
     * 用户被锁定
     */
    USER_LOCKED(11005, "用户被锁定，请联系管理员"),
    /**
     * 用户被锁定
     */
    USER_TYPE_INVALID(11006, "第三方用户不允许登录"),
    /**
     * 密码错误
     */
    USER_PASSWORD_ERROR(11007, "用户名或密码错误"),
    /**
     * 用户名错误
     */
    USER_USERNAME_ERROR(11008, "用户名或密码错误"),
    /**
     * 没有该用户
     */
    USER_NOT_EXIST(11009, "用户名不存在"),
    /**
     * 用户登录失败
     */
    USER_LOGIN_FAIL(11010, "用户登录失败"),
    /**
     * 验证码错误
     */
    VERIFY_CODE_ERROR(11011, "验证码错误"),
    /**
     * 用户已存在
     */
    USER_IS_EXIST(11012, "登录账户名称已存在"),
    /**
     * 短信调用失败
     */
    SMS_SEND_ERROR(11013, "短信调用失败"),
    /**
     * 验证码错误
     */
    USER_SMS_CODE_ERROR(11014, "验证码为空或验证码错误"),
    /**
     * 验证码不存在
     */
    USER_SMS_CODE_NULL(11015, "验证码不存在"),
    /**
     * 两次密码不等
     */
    USER_TWO_PASSWORD_VARY(11016, "两次密码不等"),
    /**
     * 用户手机已绑定
     */
    USER_MOBILE_IS_EXIST(11017, "用户手机已绑定：{0}"),
    /**
     * 用户邮箱已绑定
     */
    USER_EMAIL_IS_EXIST(11018, "邮箱已被注册"),
    /**
     * 用户不能为空
     */
    USER_COULD_NOT_BE_NULL(11019, "用户不能为空"),
    /**
     * 密码过期
     */
    USER_PASSWORD_EXPIRED(11020, "密码过期"),
    /**
     * 用户账号不存在
     */
    USER_NONE_EXIST(11021, "用户账号不存在"),
    /**
     * 原密码错误
     */
    ORIGIN_PASSWORD_ERROR(11022, "原密码错误"),
    /**
     * 原密码错误
     */
    USER_PASSWORD_RETRY_LOCK(11023, "用户已锁定{0}分钟，请稍后再试"),

    /**
     * 原密码错误
     */
    USER_PASSWORD_RETRY_ERR(11024, "密码错误，错误{0}次后将锁定用户"),
    /**
     * 用户手机已绑定
     */
    USER_EMPLOY_NUM_IS_EXIST(11025, "员工编号:{0}已经存在，请确认后重试"),
    /**
     * 用户手机已绑定
     */
    USER_IMPORT_USERTYPE_IS_EXIST(11025, "用户:{0} 用户类型为空，请确认后重试"),
    /**
     * 角色编号不能修改
     */
    ROLE_CODE_NOT_EDITABLE(12001, "角色编号不能修改"),
    /**
     * 角色已经被赋予用户
     */
    ROLE_IS_ASSIGNED(12003, "角色已经被赋予用户"),
    /**
     * 父级部门不能修改本级以及以下
     */
    CHILD_DEPART_NOT_VALID(12004, "父级部门不能修改为本级以及以下"),

    /**
     * 角色记录已存在
     */
    ROLE_IS_EXIST(12005, "角色记录已存在"),
    /**
     * 用户组记录已存在
     */
    USER_GROUP_IS_EXIST(12006, "用户组记录已存在"),
    /**
     * 角色记录已存在
     */
    DICTIONARY_IS_EXIST(12007, "字典记录已存在"),
    /**
     * 角色记录已存在
     */
    ORGAN_ROLE_CODE_NOT_EXIST(12008, "该租户下,此角色编号不存在"),
    /**
     * 应用记录已存在
     */
    APP_IS_EXIST(21001, "应用记录已存在"),
    /**
     * 应用不存在
     */
    APP_NOT_EXIST(21002, "应用不存在"),
    /**
     * 消息头appId或者appCode不存在
     */
    HEADER_APP_NOT_EXIST(21003, "消息头appId或者appCode不存在"),
    /**
     * 应用id不存在
     */
    APP_IS_NULL(21004, "资源集接口对应的应用id不存在"),
    /**
     * url格式错误
     */
    ROUTE_REGEX_FAILED(21005, "url格式错误"),
    /**
     * 消息头appId格式错误
     */
    APPID_REGEX_FAILED(21006, "消息头appId格式错误"),
    /**
     * 消息头appId或者appCode不存在
     */
    HEADER_APP_CODE_NOT_EXIST(21007, "消息头appCode不存在"),

    /**
     * 机构名称/编号已存在
     */
    ORG_IS_EXIST(31001, "租户编码不能重复"),

    /**
     * 该用户已有归属部门
     */
    USER_DEPARTMENT_EXIST(31002, "该用户已有归属部门!"),

    /**
     * 该编码部门已存在
     */
    DEPARTMENT_EXIST(31003, "该编码部门已存在!"),

    /**
     * 租户管理员不存在
     */
    ORGAN_OWNER_NOT_EXIST(31004, "租户管理员不存在，请确认后重试"),
    /**
     * 该用户并未在该租户或资源下有角色，请确认后重试
     */
    ORGAN_ROLE_NOT_EXIST(31005, "该用户并未在该租户或资源下有角色，请确认后重试"),
    /**
     * 不允许用户在租户下无角色
     */
    ORGAN_ROLE_COULD_NOT_NULL(31006, "不允许用户在租户下无角色"),
    /**
     * 用户导入，用户名为空
     */
    USER_IMPORT_USERNAME_EMPTY(31008, "用户导入，用户名为空"),
    /**
     * 用户导入，姓名为空
     */
    USER_IMPORT_NAME_EMPTY(31009, "用户导入，姓名为空"),
    /**
     * 租户导入，租户编号为空
     */
    USER_IMPORT_CODE_EMPTY(31010, "用户导入，租户编号为空"),
    /**
     * 租户导入，员工编号为空
     */
    USER_EMPLOY_NUM_EMPTY(31011, "用户导入，员工编号为空"),
    /**
     * 租户id不存在
     */
    ORGAN_NOT_EXITE(31007, "租户id不存在"),
    /**
     * 租户导入，租户编号为空
     */
    ORGAN_IMPORT_NAME_EMPTY(31009, "租户导入，租户名称为空"),
    /**
     * 租户导入，租户编号为空
     */
    ORGAN_IMPORT_CODE_EMPTY(31010, "租户导入，租户编号为空"),
    /**
     * 权限记录已存在
     */
    PERMISSION_IS_EXIST(41001, "权限记录已存在"),
    /**
     * 请先移除部门下的人员
     */
    DEPARTMENT_STAFF_EXIST(41002, "请先移除部门下的子部门或者人员"),
    /**
     * 权限拥有者类型不存在
     */
    OBJECT_PERMISSION_TYPE_NOT_EXIST(41003, "权限拥有者类型不存在"),
    /**
     * 权限记录不存在
     */
    PERMISSION_NOT_EXIST(41004, "权限记录不存在"),
    /**
     * 幂等token不能为空
     */
    IDEMPOTENT_TOKEN_IS_NULL(71001, "接口的幂等token不能为空"),
    /**
     * 重复访问接口
     */
    DUPICATION_CALL_INTERFACE(71002, "请勿频繁点击"),
    /**
     * 重复访问接口
     */
    HEADER_ORGANID_NOT_EXIST(71003, "消息头租户id不存在"),
    /**
     * 登录状态已失效或已过期
     */
    TOKEN_IS_EXPIRED(401,"登录状态已失效或已过期"),
    /**
     * 登录状态已失效或已过期(不需要跳转)
     */
    TOKEN_IS_EXPIRED_NO_REDIRECT(71004,"登录状态已失效或已过期"),
    /**
     * 登录状态已失效或已过期
     */
    YOU_HAVE_NO_FUNCTION(81001, "您没有访问此接口的权限!"),
    /**
     * 验证码为空或者验证码错误
     */
    AUTHCODE_INVAILID(81002, "验证码为空或者验证码错误!"),
    /**
     * 验证码为空或者验证码错误
     */
    ORGAN_ID_OUTOFBOUND(81003, "操作错误，请检查您的参数是否正确"),
    /**
     * 资源实例不存在或已被删除
     */
    RESOURCE_ID_NOT_EXISTS(91001, "资源实例不存在"),
    /**
     * 资源实例已存在
     */
    RESOURCE_ID_EXISTS(91002, "资源实例已存在"),

    /**
     * 资源类型编号不正确
     */
    RESOURCE_TYPE_CODE_UNVALID(91003, "资源类型编号不正确"),

    /**
     * 资源类型编号不存在
     */
    RESOURCE_TYPE_CODE_NULL(91004, "资源类型编号不存在"),
    /**
     * 资源拥有者类型不正确
     */
    TYPE_DICT_VALUE_UNVALID(91005, "资源拥有者类型不正确"),
    /**
     * 资源拥有者不存在
     */
    RESOURCE_OWNER_ID_NOT_EXISTS(91006, "资源拥有者实例不存在"),
    /**
     * 租户用户下仍关联资源实例
     */
    RESOURCE_USER_EXIST(91007, "该用户已加入项目，不可删除"),

    /**
     * 该应用下，已经存在此资源类型编码/资源类型名称
     */
    RESOURCE_TYPE_CODE_OR_NAME_IS_EXISTS_BY_APP_ID(91008, "编号已经被注册，请更换资源类型编号"),
    /**
     * 父级资源不存在
     */
    PARENT_RESOURCE_ID_NOT_EXISTS(91009, "父级资源不存在"),

    /**
     * 父级资源不存在
     */
    USER_IS_EMPTY(91010, "用户为空，请确认后重试"),
    /**
     * 用户不能为空
     */
    COMMANDER_IS_NULL(91011, "用户不能为空"),
    /**
     * 角色不能为空
     */
    ROLE_IS_NULL(91012, "角色不能为空"),
    /**
     * 资源不能为空
     */
    RESOURCE_IS_NULL(91013, "资源不能为空"),
    /**
     * 资源不能为空
     */
    ORGAN_ROLE_IS_NULL(91014, "无法删除租户下用户的最后一个角色，请移除此用户!"),
    /**
     * 资源不能为空
     */
    RESOURCE_TYPE_CODE_ROLE_ERROR(91014, "资源下角色不存在"),
    /**
     * 资源不能为空
     */
    RESOURCE_HEAD_ROLE_NOT_EXISTS(91015, "资源下负责人角色不存在"),
    /**
     * DINGDING属于关闭状态，请打开后重试
     */
    DING_TALK_NOT_OPEN(101001, "DINGDING属于关闭状态，请打开后重试"),
    /**
     * 标签类别记录在平台，租户，资源下已经存在
     */
    TAG_CLASSIFICATION_PLAT_EXITS(111001, "标签类别记录已经存在"),
    /**
     * 标签类别记录在平台，本租户下，本租户的资源下已经存在
     */
    TAG_CLASSIFICATION_ORGAN_EXITS(111002, "标签类别记录在平台，本租户下已经存在"),
    /**
     * 标签类别记录在平台，本租户下，本租户的资源下已经存在
     */
    TAG_CLASSIFICATION_RESOURCE_EXITS(111003, "标签类别记录在平台，本租户下，本租户的资源下已经存在"),
    /**
     * 标签实体不存咋
     */
    TAG_INSTANCE_ID(111004, "标签实体不存在"),
    /**
     * 标签记录在平台，租户，资源下已经存在
     */
    TAG_PLAT_EXITS(111005, "标签记录已经存在"),
    /**
     * 标签记录在平台，本租户下，本租户的资源下已经存在
     */
    TAG_ORGAN_EXITS(111006, "标签记录在平台，本租户下已经存在"),
    /**
     * 标签记录在平台，本租户下，本租户的资源下已经存在
     */
    TAG_RESOURCE_EXITS(111007, "标签记录在平台，本租户下，本租户的资源下已经存在"),
    /**
     * 标签分类记录不存在
     */
    TAG_CLASSIFICATION_NON_EXIST(111008, "标签分类不存在"),
    /**
     * 不允许编辑标签和标签分类的scope
     */
    TAG_SCOPE_CHANGE_NOT_ALLOWED(111009, "标签和标签分类的scope不允许编辑"),
    /**
     * 新增的时候，标签的scope不能低于标签分类的scope
     */
    TAG_SCOPE_HIGHER_THAN_CLASS_NOT_ALLOWED(111010, "标签的scope不允许低于标签分类的scope"),
    /**
     * 模版名称已经存在
     */
    TEMPLATE_IS_EXISTS(121001, "模版名称已经存在"),
    /**
     * 模版名称已经存在
     */
    TEMPLATE_NOT_EXISTS(121002, "模版不存在"),

    /**
     * 功能模块名称/编号已经存在
     */
    CATEGORY_IS_EXISTS(131001, "功能模块名称/编号已经存在"),

    /**
     * 功能模块编号不存在
     */
    CATEGORY_NOT_EXISTS(131002, "功能模块编号不存在"),

    /**
     * 触发事件名称/编号已经存在
     */
    TRIGGER_IS_EXISTS(141001, "触发事件名称/编号已经存在"),

    /**
     *  触发事件编号不存在
     */
    TRIGGER_NOT_EXISTS(141002, " 触发事件编号不存在"),

    /**
     *  接收人不存在
     */
    RECEIVER_NOT_EXISTS(141003, "接收人不存在"),

    /**
     *  请先配置邮件服务器或者发送邮件接口
     */
    EMAIL_CONFIG_ERROR(141004, "请先配置邮件服务器或者发送邮件接口"),

    /**
     *  标题不能为空
     */
    TITLE_NOT_EXISTS(141005, "标题不能为空"),
    /**
     *  角色类型roleTypeCode不存在
     */
    ROLE_TYPE_CODE_NOT_EXISTS(151001, " 角色类型roleTypeCode不存在"),

    /**
     * userWide表新增校验uniqueId
     */
    ASSOCIATED_PROBLEM_USER_NOT_ALLOWED(201011, "已经存在异常用户"),
    /**
     * 任务已经执行，请勿重复提交
     */
    TASK_ALREADY_START(201012, "任务已经执行，请勿重复提交"),
    /**
     * 通用配置已经存在
     */
    COMMON_CONFIG_IS_EXISTS(201013,"通用配置已经存在"),
    /**
     * 文件后缀名错误
     */
    FILE_SUFFIX_ERROR(211001,"请上传zip文件"),
    /**
     * 文件上传记录不存在
     */
    FILE_RECORD_ERROR(211002,"文件上传记录不存在"),
    /**
     * 文件名称已经存在
     */
    FILE_NAME_EXIST(211003,"文件名称已经存在,请修改名称后重试"),
    /**
     * 文件名称已经存在
     */
    ADMIN_PARENT_ID_NOT_EXIST(231001,"父级数据不存在"),
    /**
     * 行政组织编码重复
     */
    ADMIN_CODE_IS_EXIST(232002,"行政组织编码：{0} 重复"),
    /**
     * 行政组织编码重复
     */
    ADMIN_IMPORT_CHECK_FAIL(232003,"{0}"),
    /**
     * ip格式错误
     */
    IP_REGEX_ERROR(221001,"ip:{0}格式错误，请确认后重试"),
    /**
     * 在强管控租户ip范围内
     */
    IP_IN_FORCED_CONTROL(221002,"ip:{0}在强管控租户ip范围内，请确认后重试"),
    /**
     * ip跟现有的强管控租户的ip冲突，请删除后重试
     */
    IP_DUPLICATE_FORCED_CONTROL(221003,"ip跟现有的强管控租户的ip冲突，请删除后重试"),
    /**
     * ip跟现有的强管控租户的ip冲突，请删除后重试
     */
    IP_REPEAT(221004,"ip:{0}在当前租户下已存在，请确认后重试"),
    /**
     * ip白名单为空，请确认后重试
     */
    IP_IS_NULL(221005,"ip白名单为空，请确认后重试"),
    ;


    final int code;
    final String message;
}
