package com.trinasolar.integration.api.resq;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName Categories
 * @Description
 * <AUTHOR>
 * @Date 2025/5/14 15:38
 **/
@Data
public class Categories implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    private String description;

    private Long parentId;

    private Integer sort;

    private String userCode;

    private String userRealname;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;

    private Integer delFlag;
}
