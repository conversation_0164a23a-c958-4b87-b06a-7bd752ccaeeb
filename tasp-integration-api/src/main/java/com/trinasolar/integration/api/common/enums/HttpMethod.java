package com.trinasolar.integration.api.common.enums;

import java.util.Arrays;
import java.util.Optional;

/**
 * HTTP方法枚举类，封装常见的HTTP请求方法
 */
public enum HttpMethod {
    /**
     * GET方法，用于获取资源
     */
    GET("GET"),
    
    /**
     * POST方法，用于创建资源
     */
    POST("POST"),
    
    /**
     * PUT方法，用于更新资源
     */
    PUT("PUT"),
    
    /**
     * DELETE方法，用于删除资源
     */
    DELETE("DELETE"),
    
    /**
     * PATCH方法，用于部分更新资源
     */
    PATCH("PATCH"),
    
    /**
     * HEAD方法，用于获取资源头部信息
     */
    HEAD("HEAD"),
    
    /**
     * OPTIONS方法，用于获取资源支持的HTTP方法
     */
    OPTIONS("OPTIONS");

    private final String value;

    HttpMethod(String value) {
        this.value = value;
    }

    /**
     * 获取HTTP方法的字符串值
     * @return HTTP方法字符串
     */
    public String getValue() {
        return value;
    }

    /**
     * 根据字符串值获取对应的HTTP方法枚举
     * @param value HTTP方法字符串
     * @return 对应的HTTP方法枚举，如果不存在则返回空
     */
    public static Optional<HttpMethod> fromValue(String value) {
        return Arrays.stream(values())
                .filter(method -> method.value.equalsIgnoreCase(value))
                .findFirst();
    }

    /**
     * 检查字符串值是否是有效的HTTP方法
     * @param value 要检查的字符串
     * @return 如果是有效的HTTP方法则返回true，否则返回false
     */
    public static boolean isValid(String value) {
        return fromValue(value).isPresent();
    }
}