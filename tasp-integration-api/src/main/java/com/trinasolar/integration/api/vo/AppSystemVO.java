package com.trinasolar.integration.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Data
@Accessors(chain = true)
@Schema(description = "应用简略信息")
public class AppSystemVO {
    private static final long serialVersionUID = -4532784790141481074L;

    @Schema(description = "应用ID")
    private Long id;

    @Schema(description = "应用中文名称")
    private String cnName;

    @Schema(description = "应用英文名称")
    private String enName;

    @Schema(description = "应用所属业务域名")
    private String businessDomain;

    @Schema(description = "业务负责人")
    private User businessLeader;

    @Schema(description = "技术负责人")
    private User technicalLeader;

    @Schema(description = "运维负责人")
    private User omLeader;

    @Schema(description = "系统负责人")
    private User systemLeader;

    @Schema(description = "应用系统管理员")
    private User adminLeader;

}
