package com.trinasolar.integration.api.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GatewayDTO {
    // 主键
    private Long id;
    // 绑定显示名称
    private String showName;
    // 绑定显示地址
    private String showUrl;
    // APIC的网关名字
    private String gatewayName;
    // APIC的网关地址
    private String gatewayUrl;
    // 状态（0正常 1停用）
    private Integer status;
    // 创建时间
    private String createTime;
}
