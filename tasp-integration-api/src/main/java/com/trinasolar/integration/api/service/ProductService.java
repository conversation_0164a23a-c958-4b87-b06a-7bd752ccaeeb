package com.trinasolar.integration.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.integration.api.entity.ProductServicePO;
import com.trinasolar.integration.api.entity.ShareComponentDO;

import java.util.List;

public interface ProductService extends IService<ProductServicePO> {
    List<ProductServicePO> getAllProducts();

    int addComponentToProduct(ShareComponentDO shareComponentDO);

    int updateComponentToProduct(ShareComponentDO shareComponentDO);

    int deleteComponentToProduct(Long id);
}
