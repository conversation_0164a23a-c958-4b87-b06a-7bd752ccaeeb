package com.trinasolar.integration.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "更新成员请求DTO")
public class UpdateMemberDTO {

    @NotNull(message = "应用系统id不能为空")
    @Schema(description = "应用系统id", required = true)
    private Long systemId;

    @NotBlank(message = "新增成员用户编码不能为空")
    @Schema(description = "新增成员用户编码（多个用逗号分隔）", required = true)
    private String incrUserCodes;

    @NotBlank(message = "新增成员git账号不能为空")
    @Schema(description = "新增成员git账号", required = true)
    private String incrUserNames;

    @NotBlank(message = "业务域不能为空")
    @Schema(description = "业务域", required = true)
    private String businessDomain;

    @NotBlank(message = "应用系统英文名称不能为空")
    @Schema(description = "应用系统英文名称", required = true)
    private String namespace;

    @NotBlank(message = "删除用户编码不能为空")
    @Schema(description = "删除用户编码（多个用逗号分隔）", required = true)
    private String delUserCodes;

    @NotBlank(message = "删除用户git账号不能为空")
    @Schema(description = "删除用户git账号", required = true)
    private String delUserNames;
}
