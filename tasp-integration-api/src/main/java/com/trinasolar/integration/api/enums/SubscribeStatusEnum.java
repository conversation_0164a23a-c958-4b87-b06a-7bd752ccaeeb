package com.trinasolar.integration.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum SubscribeStatusEnum {

    APPLYING(0, "申请中"),
    APPROVED(1, "通过"),
    REJECTED(2, "拒绝");

    @EnumValue
    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static SubscribeStatusEnum of(Integer code) {
        for (SubscribeStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的订阅状态码: " + code);
    }
}