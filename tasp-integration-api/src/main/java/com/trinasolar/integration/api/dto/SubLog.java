package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class SubLog {
    // 请求时间
    @JsonAlias("datetime")
    private String datetime;

    // 请求方式
    @JsonAlias("request_method")
    private String requestMethod;

    // 状态码
    @JsonAlias("status_code")
    private String statusCode;

    // 耗时(毫秒)
    @JsonAlias("time_to_serve_request")
    private Integer timeToServeRequest;

    // 请求URI路径
    @JsonAlias("uri_path")
    private String uriPath;

    // 请求ID
    @JsonAlias("event_id")
    private String eventId;

    // 产品标题
    @JsonAlias("product_title")
    private String productTitle;
}
