package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trinasolar.integration.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 系统配置信息 DO
 *
 * <AUTHOR>
 */
@TableName("tasp_develop.tasc_app_config")
@KeySequence("tasc_app_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppConfigDO extends BaseDO {

    /**
     * 主键 
     */
    @TableId
    private Long id;
    /**
     * 系统id
     */
    private Long appId;
    /**
     * 系统id
     */
    private Long projectId;
    /**
     * 配置Key
     */
    private String configKey;
    /**
     * 配置名称
     */
    private String configName;
    /**
     * 配置内容
     */
    private String configContent;
    /**
     * 系统描述
     */
    private String description;
    /**
     * 是否默认，默认的不允许改
     */
    private Integer isDefault;
    /**
     * 系统类型
     */
    private Integer type;
    /**
     * 负责人
     */
    private String masterName;
    /**
     * 状态（0正常 1停用）
     *
     * 枚举 {@link  common_status 对应的类}
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除时间
     */
    private LocalDateTime deletedTime;

}