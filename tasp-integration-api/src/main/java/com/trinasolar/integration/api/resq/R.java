package com.trinasolar.integration.api.resq;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-04-27 10:19
 */
@Getter
@Setter
@ToString
@Slf4j
public final class R<T> implements Serializable {

    private static final long serialVersionUID = 7282768964875792176L;

    private int code;

    private String msg;

    private T data;

    private R() {
    }

    private R(int code, String message) {
        this.code = code;
        this.msg = message;
    }

    private R(int code, String message, T data) {
        this.code = code;
        this.msg = message;
        this.data = data;
    }

    private R(ResultInterface result) {
        this.code = result.getCode();
        this.msg = result.getMessage();
    }

    private R(ResultInterface result, String message) {
        this.code = result.getCode();
        this.msg = message;
    }

    private R(ResultInterface result, T data) {
        this.code = result.getCode();
        this.msg = result.getMessage();
        this.data = data;
    }

    public R(ResultEnum resultEnum) {
    }

    public R(ResultEnum resultEnum, String message) {
    }

    public static <T> R<T> success() {
        return new R<>(ResultEnum.SUCCESS);
    }

    public static <T> R<T> success(String message) {
        return new R<>(ResultEnum.SUCCESS, message);
    }

    public static <T> R<T> data(T data) {
        return new R<>(ResultEnum.SUCCESS, String.valueOf(data));
    }

    public static <T> R<T> failure() {
        return new R<>(ResultEnum.FAILURE);
    }

    public static <T> R<T> failure(String message) {
        return new R<>(ResultEnum.FAILURE, message);
    }

    public static <T> R<T> failure(int code, String message) {
        return new R<>(code, message);
    }

    public static <T> R<T> failure(int code, String message, T data) {
        return new R<>(code, message, data);
    }

    public static <T> R<T> failure(ResultInterface result) {
        return new R<>(result);
    }

    public static <T> R<T> status(HttpStatus httpStatus) {
        return new R<>(httpStatus.value(), httpStatus.getReasonPhrase());
    }

    public static <T> R<T> condition(boolean result) {
        return result ? success() : failure();
    }


    /**
     * 获取返回数据
     * @return
     * @throws Exception
     */
    public T checkAndGetData() {
        if(ResultEnum.SUCCESS.code== this.code){
            return this.data;
        }else{
            log.error("获取接口返回数据报错:"+this.msg);
        }
        return null;
    }

    /**
     * 获取返回数据
     * @return
     * @throws Exception
     */
    public T checkIfNullDefaultAndGetData(T t) {
        if(ResultEnum.SUCCESS.code== this.code){
            return this.data;
        }else{
            log.error("获取接口返回数据报错:"+this.msg);
        }
        return t;
    }


    public boolean isSuccess() {
        return ResultEnum.SUCCESS.code == this.code;
    }
}
