package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tasp_base.inte_app_sync_log")
public class AppSyncLog {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

   @Schema(description= "应用ID")
    private Long appId;

   @Schema(description= "应用中文名称")
    private String appName;

   @Schema(description= "下游系统名称")
    private String downStreamName;

   @Schema(description= "创建用户ID")
    private Long creatorId;

   @Schema(description= "创建用户名称")
    private String creatorName;

   @Schema(description= "同步时间")
    private LocalDateTime createdTime;

   @Schema(description= "同步状态")
    private String syncStatus;
}
