# Final stage
FROM bizdevops-crepo.trinasolar.com/a355b7/docker-local/eclipse-temurin:11-jdk-arthas-sky-ip

ARG JVM_MEM_ARGS="-Xmx1024m -Xms512m"
ENV JAR_FILE=/home/<USER>/springboot-dockerfile.jar
ENV HOST_IP=""
ADD ./tasp-integration-server/target/*.jar /home/<USER>/springboot-dockerfile.jar
COPY ./tasp-integration-server/src/main/resources/pipeline-templates /tmp/pipeline-templates
EXPOSE 8081
COPY start-hc.sh ./start.sh
# start the script to run your application. Take note that there are several other optional environment variables supported
# for details, please see the start-hc.sh.

ENTRYPOINT ["/bin/bash", "start.sh"]
