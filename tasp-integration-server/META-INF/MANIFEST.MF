Manifest-Version: 1.0
Created-By: <PERSON><PERSON> 3.2.0
Build-Jdk-Spec: 11
Class-Path: tasp-spring-boot-starter-web-1.0.0.jar spring-boot-starter-w
 eb-2.7.18.jar spring-boot-starter-json-2.7.18.jar jackson-datatype-jdk8
 -2.13.5.jar jackson-datatype-jsr310-2.13.5.jar jackson-module-parameter
 -names-2.13.5.jar spring-boot-starter-tomcat-2.7.18.jar tomcat-embed-co
 re-9.0.83.jar tomcat-embed-websocket-9.0.83.jar spring-web-5.3.39.jar s
 pring-beans-5.3.39.jar spring-webmvc-5.3.39.jar spring-aop-5.3.39.jar s
 pring-context-5.3.39.jar spring-expression-5.3.39.jar knife4j-openapi3-
 spring-boot-starter-4.5.0.jar knife4j-core-4.5.0.jar knife4j-openapi3-u
 i-4.5.0.jar springdoc-openapi-ui-1.7.0.jar springdoc-openapi-webmvc-cor
 e-1.7.0.jar springdoc-openapi-common-1.7.0.jar swagger-core-2.2.9.jar j
 ackson-dataformat-yaml-2.13.5.jar swagger-annotations-2.2.9.jar swagger
 -models-2.2.9.jar swagger-ui-4.18.2.jar jsoup-1.18.1.jar tasp-common-1.
 0.0.jar lombok-1.18.36.jar mapstruct-1.6.3.jar mapstruct-jdk8-1.6.3.jar
  mapstruct-processor-1.6.3.jar hutool-all-5.8.35.jar transmittable-thre
 ad-local-2.14.5.jar easy-trans-anno-3.0.6.jar jackson-annotations-2.13.
 5.jar fastjson-1.2.83.jar tasp-integration-api-1.0.0.jar spring-cloud-s
 tarter-openfeign-3.1.8.jar spring-cloud-starter-3.1.7.jar spring-securi
 ty-rsa-1.0.11.RELEASE.jar bcpkix-jdk15on-1.69.jar bcprov-jdk15on-1.69.j
 ar bcutil-jdk15on-1.69.jar spring-cloud-openfeign-core-3.1.8.jar spring
 -boot-starter-aop-2.7.18.jar aspectjweaver-1.9.7.jar feign-form-spring-
 3.8.0.jar feign-form-3.8.0.jar commons-fileupload-1.5.jar feign-slf4j-1
 1.10.jar feign-core-11.10.jar feign-jackson-11.10.jar jackson-databind-
 2.13.5.jar feign-httpclient-11.10.jar httpclient-4.5.14.jar httpcore-4.
 4.16.jar spring-boot-starter-validation-2.7.18.jar spring-boot-starter-
 2.7.18.jar spring-boot-2.7.18.jar spring-boot-starter-logging-2.7.18.ja
 r logback-classic-1.2.13.jar logback-core-1.2.13.jar log4j-to-slf4j-2.1
 7.2.jar jul-to-slf4j-1.7.36.jar jakarta.annotation-api-1.3.5.jar spring
 -core-5.3.39.jar spring-jcl-5.3.39.jar snakeyaml-1.30.jar tomcat-embed-
 el-9.0.83.jar hibernate-validator-6.2.5.Final.jar jakarta.validation-ap
 i-2.0.2.jar jboss-logging-3.4.3.Final.jar classmate-1.5.1.jar easyexcel
 -4.0.3.jar easyexcel-core-4.0.3.jar easyexcel-support-3.3.4.jar poi-5.2
 .5.jar commons-collections4-4.4.jar commons-math3-3.6.1.jar commons-io-
 2.17.0.jar SparseBitSet-1.3.jar log4j-api-2.17.2.jar xmlbeans-5.2.0.jar commons-compress-1.27.1.j
 ar commons-lang3-3.12.0.jar curvesapi-1.08.jar commons-csv-1.11.0.jar e
 hcache-3.10.8.jar cache-api-1.1.1.jar jaxb-runtime-2.3.9.jar jakarta.xm
 l.bind-api-2.3.3.jar txw2-2.3.9.jar istack-commons-runtime-3.0.12.jar j
 akarta.activation-1.2.2.jar slf4j-api-1.7.36.jar spring-cloud-starter-a
 libaba-nacos-discovery-2021.0.5.0.jar spring-cloud-alibaba-commons-2021
 .0.5.0.jar nacos-client-2.2.0.jar nacos-auth-plugin-2.2.0.jar nacos-enc
 ryption-plugin-2.2.0.jar commons-codec-1.15.jar jackson-core-2.13.5.jar
  httpasyncclient-4.1.5.jar httpcore-nio-4.4.16.jar simpleclient-0.15.0.
 jar simpleclient_tracer_otel-0.15.0.jar simpleclient_tracer_common-0.15
 .0.jar simpleclient_tracer_otel_agent-0.15.0.jar spring-context-support
 -1.0.11.jar spring-cloud-commons-3.1.7.jar spring-security-crypto-5.7.1
 1.jar spring-cloud-context-3.1.7.jar spring-cloud-starter-alibaba-nacos
 -config-2021.0.5.0.jar mybatis-3.5.17.jar mybatis-plus-boot-starter-3.5
 .9.jar mybatis-plus-3.5.9.jar mybatis-plus-core-3.5.9.jar mybatis-plus-
 annotation-3.5.9.jar mybatis-plus-spring-3.5.9.jar mybatis-spring-2.1.2
 .jar mybatis-plus-spring-boot-autoconfigure-3.5.9.jar spring-boot-autoc
 onfigure-2.7.18.jar spring-boot-starter-jdbc-2.7.18.jar HikariCP-4.0.3.
 jar spring-jdbc-5.3.39.jar spring-tx-5.3.39.jar mybatis-plus-jsqlparser
 -4.9-3.5.9.jar jsqlparser-4.9.jar mybatis-plus-jsqlparser-common-3.5.9.
 jar mybatis-plus-extension-3.5.9.jar mybatis-plus-generator-3.5.9.jar m
 ybatis-plus-join-boot-starter-1.4.13.jar mybatis-plus-join-extension-1.
 4.13.jar mybatis-plus-join-core-1.4.13.jar mybatis-plus-join-annotation
 -1.4.13.jar mybatis-plus-join-adapter-v33x-1.4.13.jar mybatis-plus-join
 -adapter-base-1.4.13.jar mybatis-plus-join-adapter-jsqlparser-1.4.13.ja
 r mybatis-plus-join-adapter-jsqlparser-v46-1.4.13.jar mybatis-plus-join
 -adapter-v3431-1.4.13.jar mybatis-plus-join-adapter-v352-1.4.13.jar myb
 atis-plus-join-adapter-v355-1.4.13.jar mysql-connector-j-8.0.33.jar
Implementation-Title: kepler-integration-server
Implementation-Version: 1.0.0
Main-Class: com.trinasolar.integration.KeplerIntegrationServerApplicatio
 n

