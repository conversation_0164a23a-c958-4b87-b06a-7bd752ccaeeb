2025-09-03 11:39:03.029 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-03 11:39:03.148 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-03 11:39:03.151 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:03.151 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Inlined Test Properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:03.151 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:03.152 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:03.152 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-03 11:39:03.153 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 11:39:03.153 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:03.153 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:03.239 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-03 11:39:03.249 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-03 11:39:03.254 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-03 11:39:03.856 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-03 11:39:03.891 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-03 11:39:03.921 | [31m WARN 23012[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-03 11:39:03.939 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-03 11:39:03.981 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-03 11:39:03.983 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.t.i.s.sca.impl.SCAServiceImplTest     [0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-03 11:39:05.367 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 11:39:05.371 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 11:39:05.434 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
2025-09-03 11:39:05.744 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=e28b5ac8-0356-3407-b0d1-eb1c63d21933
2025-09-03 11:39:05.881 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-03 11:39:05.894 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-03 11:39:05.894 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource test [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:05.895 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 11:39:05.895 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 11:39:05.895 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 11:39:05.895 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Inlined Test Properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:05.895 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 11:39:05.895 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 11:39:05.895 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:05.896 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:05.896 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-03 11:39:05.896 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 11:39:05.896 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:05.896 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:39:05.910 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-03 11:39:05.923 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-03 11:39:05.924 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-03 11:39:06.611 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 11:39:06.615 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 11:39:06.617 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$964/0x00000008006e4c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 11:39:06.620 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 11:39:06.766 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-03 11:39:06.884 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-03 11:39:07.532 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-03 11:39:07.533 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-03 11:39:07.533 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-03 11:39:07.615 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-03 11:39:10.718 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-03 11:39:10.729 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-03 11:39:10.729 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-03 11:39:10.729 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-03 11:39:10.730 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-03 11:39:10.730 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-03 11:39:10.730 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-03 11:39:10.731 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-03 11:39:10.731 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-03 11:39:11.245 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-03 11:39:11.366 | [31m WARN 23012[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-03 11:39:11.691 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756870749000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756870750000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756870750000 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756870749303 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756870751310 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756870750307 3 connected

2025-09-03 11:39:11.754 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-03 11:39:11.820 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-03 11:39:11.824 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-03 11:39:12.382 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-03 11:39:12.506 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-29[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-03 11:39:12.506 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-5[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-03 11:39:12.711 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-9[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-03 11:39:12.754 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-03 11:39:12.766 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-1[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-03 11:39:13.006 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-24[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-03 11:39:13.007 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-24[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-03 11:39:13.008 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-24[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-03 11:39:13.094 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-9[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-03 11:39:13.094 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-9[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-03 11:39:13.095 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-9[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-03 11:39:13.229 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-03 11:39:13.229 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-03 11:39:13.229 | [34m INFO 23012[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-03 11:39:15.066 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-03 11:39:15.842 | [31m WARN 23012[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-03 11:39:15.920 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-03 11:39:15.921 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-03 11:39:15.922 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-03 11:39:16.162 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"*************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"*************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 11:39:16.169 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"*************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"*************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 11:39:16.185 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.t.i.s.sca.impl.SCAServiceImplTest     [0;39m | Started SCAServiceImplTest in 14.296 seconds (JVM running for 16.297)
2025-09-03 11:39:16.204 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-03 11:39:16.205 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-03 11:39:16.206 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-03 11:39:16.206 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-03 11:39:17.386 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.t.i.service.impl.SCAServiceImpl       [0;39m | createModel body:{"auto_lang":1,"branch":"develop","name":"测试模块tasp8279","passwd":"**************************","path":"https://code.trinasolar.com/tasptest/devops-tes/backend62505.git","project_id":293,"username":"root"}
2025-09-03 11:39:17.910 | [34m INFO 23012[0;39m | [1;33mmain[0;39m [1;32mc.t.i.service.impl.SCAServiceImpl       [0;39m | createModel result:{"code":42200,"data":{"id":4665},"error":"","message":"添加模块成功","ok":true,"success":true}
2025-09-03 11:39:17.929 | [31m WARN 23012[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-03 11:39:17.929 | [31m WARN 23012[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-03 11:39:17.930 | [31m WARN 23012[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-03 11:39:17.930 | [31m WARN 23012[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-03 11:39:17.938 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-03 11:39:17.938 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-03 11:39:17.938 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-03 11:39:20.946 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-03 11:39:23.952 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-03 11:39:23.953 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-03 11:39:26.065 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-03 11:39:26.065 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-03 11:39:26.065 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-03 11:39:26.066 | [31m WARN 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-03 11:39:26.066 | [31m WARN 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-03 11:39:26.066 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-03 11:39:26.066 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-03 11:39:26.066 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-03 11:39:26.111 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-03 11:39:26.114 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-03 11:39:26.124 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-03 11:39:26.124 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-03 11:39:26.124 | [34m INFO 23012[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-03 11:41:13.491 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-03 11:41:13.585 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-03 11:41:13.587 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:13.587 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Inlined Test Properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:13.587 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:13.588 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:13.589 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-03 11:41:13.589 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 11:41:13.589 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:13.590 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:13.682 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-03 11:41:13.690 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-03 11:41:13.693 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-03 11:41:14.221 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-03 11:41:14.253 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-03 11:41:14.281 | [31m WARN 23897[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-03 11:41:14.298 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-03 11:41:14.339 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-03 11:41:14.341 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.t.i.s.sca.impl.SCAServiceImplTest     [0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-03 11:41:15.744 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 11:41:15.750 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 11:41:15.812 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
2025-09-03 11:41:16.145 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=e28b5ac8-0356-3407-b0d1-eb1c63d21933
2025-09-03 11:41:16.262 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-03 11:41:16.282 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-03 11:41:16.282 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource test [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:16.283 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 11:41:16.283 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 11:41:16.283 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 11:41:16.283 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Inlined Test Properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:16.283 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 11:41:16.284 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 11:41:16.284 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:16.284 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:16.284 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-03 11:41:16.284 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 11:41:16.284 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:16.284 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 11:41:16.297 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-03 11:41:16.309 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-03 11:41:16.309 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-03 11:41:16.997 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 11:41:17.001 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 11:41:17.004 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$964/0x00000008006e4040] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 11:41:17.008 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 11:41:17.166 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-03 11:41:17.320 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-03 11:41:18.036 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-03 11:41:18.037 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-03 11:41:18.037 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-03 11:41:18.130 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-03 11:41:21.282 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-03 11:41:21.295 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-03 11:41:21.295 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-03 11:41:21.295 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-03 11:41:21.295 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-03 11:41:21.296 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-03 11:41:21.296 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-03 11:41:21.297 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-03 11:41:21.297 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-03 11:41:21.895 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-03 11:41:22.023 | [31m WARN 23897[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-03 11:41:22.343 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756870881000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756870880000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756870879000 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756870880841 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756870881846 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756870879000 3 connected

2025-09-03 11:41:22.428 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-03 11:41:22.433 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-03 11:41:22.440 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-03 11:41:23.011 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-03 11:41:23.019 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-03 11:41:23.069 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-4[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-03 11:41:23.069 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-28[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-03 11:41:23.226 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-7[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-03 11:41:23.301 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-03 11:41:23.577 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-3[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-03 11:41:23.578 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-3[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-03 11:41:23.578 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-3[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-03 11:41:23.603 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-03 11:41:23.603 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-03 11:41:23.603 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-03 11:41:23.764 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-03 11:41:23.764 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-03 11:41:23.765 | [34m INFO 23897[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-03 11:41:25.734 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-03 11:41:26.531 | [31m WARN 23897[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-03 11:41:26.611 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-03 11:41:26.612 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-03 11:41:26.612 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-03 11:41:26.872 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"*************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"*************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-03 11:41:26.879 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"*************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"*************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-03 11:41:26.896 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.t.i.s.sca.impl.SCAServiceImplTest     [0;39m | Started SCAServiceImplTest in 14.456 seconds (JVM running for 16.184)
2025-09-03 11:41:26.915 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-03 11:41:26.916 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-03 11:41:26.917 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-03 11:41:26.917 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-03 11:41:28.151 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.t.i.service.impl.SCAServiceImpl       [0;39m | createModel body:{"auto_lang":1,"branch":"develop","name":"测试模块tasp0903","passwd":"**************************","path":"https://code.trinasolar.com/tasptest/devops-tes/backend62505.git","project_id":288,"username":"root"}
2025-09-03 11:41:28.594 | [34m INFO 23897[0;39m | [1;33mmain[0;39m [1;32mc.t.i.service.impl.SCAServiceImpl       [0;39m | createModel result:{"code":42200,"data":{"id":4666},"error":"","message":"添加模块成功","ok":true,"success":true}
2025-09-03 11:41:28.616 | [31m WARN 23897[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-03 11:41:28.616 | [31m WARN 23897[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-03 11:41:28.616 | [31m WARN 23897[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-03 11:41:28.617 | [31m WARN 23897[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-03 11:41:28.625 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-03 11:41:28.625 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-03 11:41:28.625 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-03 11:41:31.634 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-03 11:41:34.642 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-03 11:41:34.642 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-03 11:41:36.775 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-03 11:41:36.775 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-03 11:41:36.775 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-03 11:41:36.775 | [31m WARN 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-03 11:41:36.775 | [31m WARN 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-03 11:41:36.775 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-03 11:41:36.776 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-03 11:41:36.776 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-03 11:41:36.819 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-03 11:41:36.822 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-03 11:41:36.831 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-03 11:41:36.831 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-03 11:41:36.831 | [34m INFO 23897[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
