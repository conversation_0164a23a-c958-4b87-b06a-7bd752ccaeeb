package com.trinasolar.integration.constants;

import com.trinasolar.tasc.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {
    ErrorCode PROJECT_NOT_EXISTS = new ErrorCode(1_003_000_001, "项目信息不存在或者无权限访问");
    ErrorCode PROJECT_CODE_NOT_UNIQUE = new ErrorCode(1_003_000_002, "项目编码重复");

    ErrorCode PROJECT_CONFIG_NOT_EXISTS = new ErrorCode(1_003_001_001, "项目配置信息不存在或者无权限访问");

    ErrorCode PROJECT_CONFIG_NOT_ALLOW_EDIT = new ErrorCode(1_003_001_004, "默认项目配置不允许更改");

    ErrorCode PROJECT_CONFIG_NOT_UNIQUE = new ErrorCode(1_003_001_002, "该项目的ConfigKey已存在，不能重复配置");
    ErrorCode PROJECT_CONFIG_INVALID = new ErrorCode(1_003_001_003, "项目的配置信息格式不正确");
    ErrorCode APP_NOT_EXISTS = new ErrorCode(1_003_002_001, "系统信息不存在或者无权限访问");

    ErrorCode APP_TEMPLATE_NOT_SUPPORT = new ErrorCode(1_003_002_002, "应用模板暂不支持，目前仅支持SpringBoot单体模板");
    ErrorCode APP_TEMPLATE_ERROR = new ErrorCode(1_003_002_003, "应用模板生成失败");

    ErrorCode APP_CONFIG_NOT_EXISTS = new ErrorCode(1_003_003_001, "系统配置信息不存在或者无权限访问");
    ErrorCode APP_CONFIG_NOT_ALLOW_EDIT = new ErrorCode(1_003_003_002, "系统配置信息不允许编辑");
    ErrorCode APP_CONFIG_NOT_UNIQUE = new ErrorCode(1_003_003_003, "系统配置信息已存在，不允许重复");

    ErrorCode PERMISION_NOT_EXISTS = new ErrorCode(1_003_004_001, "项目权限不存在");
    ErrorCode PERMISION_ALREADY_EXISTS = new ErrorCode(1_003_004_002, "该用户的项目权限已存在，禁止重复添加");

}