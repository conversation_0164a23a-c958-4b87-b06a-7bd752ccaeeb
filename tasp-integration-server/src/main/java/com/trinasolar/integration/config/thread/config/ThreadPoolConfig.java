package com.trinasolar.integration.config.thread.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import java.util.concurrent.ThreadPoolExecutor;
@Slf4j
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    @Primary
    @Bean(name = "taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(4);
        // 最大线程数
        executor.setMaxPoolSize(20);
        // 队列容量
        executor.setQueueCapacity(1000);
        // 线程名前缀
        executor.setThreadNamePrefix("async-task-");
        // 拒绝策略：由调用线程处理该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        // 2. 带日志记录的拒绝策略（推荐方案）
        executor.setRejectedExecutionHandler((r, e) -> {
            // 记录任务被拒绝的日志
            System.err.println("Task rejected: " + r.toString());
            if (!e.isShutdown()) {
                // 可以在此添加重试逻辑或报警机制
                r.run(); // 仍使用调用者线程执行
            }
        });
        // 初始化
        executor.initialize();
        return executor;
    }
}