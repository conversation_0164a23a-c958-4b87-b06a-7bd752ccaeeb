package com.trinasolar.integration.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AppTemplateTypeEnum {

    SPRINGBOOT(1, "SpringBoot单体版本", "SpringBoot单体应用", "后端"),

    SPRINGCLOUD(2, "SpringCloud微服务版本", "SpringCloud微服务架构", "后端"),

    VUE(3, "Vue Element Plus", "Vue3+ElementPlus前端模板", "前端"),

    REACT(4, "React版本", "React18前端模板", "前端");

    private final Integer id;
    private final String name;
    private final String description;
    private final String type;

    public static AppTemplateTypeEnum fromName(String name) {
        for (AppTemplateTypeEnum type : values()) {
            if (type.name.toLowerCase().contains(name.toLowerCase())) {
                return type;
            }
        }
        return null;
    }

    public static AppTemplateTypeEnum fromId(Integer id) {
        for (AppTemplateTypeEnum type : values()) {
            if (type.id.equals(id)) {
                return type;
            }
        }
        return null;
    }
}
