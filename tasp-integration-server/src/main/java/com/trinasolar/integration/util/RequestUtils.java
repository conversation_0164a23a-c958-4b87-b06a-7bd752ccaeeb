package com.trinasolar.integration.util;


import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class RequestUtils implements ApplicationContextAware {

    public static final String AUTHORIZATION = "Authorization";

    private volatile static ApplicationContext applicationContext;

    /**
     * 获取当前请求的HttpServletRequest对象
     */
    public static HttpServletRequest getRequest() {
        return ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
    }

    /**
     * 从请求头获取指定参数
     *
     * @param headerName 请求头名称
     */
    public static String getHeader(String headerName) {
        HttpServletRequest request = getRequest();
        return request != null ? request.getHeader(headerName) : null;
    }

    /**
     * 从请求头获取token
     */
    public static String getToken() {
        String token = getHeader(AUTHORIZATION);
        return StringUtils.isNotBlank(token) ? token.trim() : null;
    }

    /**
     * 重当前请求头中获取token
     *
     * @return
     */
    public static User getCurrentUser() {
        UserService userService = applicationContext.getBean("userService", UserService.class);
        return userService.getUser();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        RequestUtils.applicationContext = applicationContext;
    }
}