package com.trinasolar.integration.server.util;

import java.util.concurrent.ThreadLocalRandom;

/**
 * 随机数字生成工具
 */
public class RandomIdUtil {

    private RandomIdUtil() {
    }

    private static final String CHAR_SET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    /**
     * 生成指定位数的随机数
     *
     * @param number
     * @return
     */
    public static String generate(int number) {
        if (number <= 0) {
            number = 8;
        }
        StringBuilder sb = new StringBuilder(number);
        ThreadLocalRandom random = ThreadLocalRandom.current();
        for (int i = 0; i < number; i++) {
            sb.append(CHAR_SET.charAt(random.nextInt(CHAR_SET.length())));
        }
        return sb.toString();
    }

    public static String generate() {
        return generate(8);
    }
}
