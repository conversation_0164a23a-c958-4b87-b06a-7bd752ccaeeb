package com.trinasolar.integration.util;

import cn.hutool.core.codec.Base64;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class DateUtil {

    private DateUtil() {
    }

    public static String convertWithPattern(Long timestamp) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 步骤2：转换为带时区的对象并格式化
        String formattedTime = Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.systemDefault())  // 默认时区
                .format(formatter);
        return formattedTime;
    }

//    public static void main(String[] args) {
//        String str = "<EMAIL>:tZN*578e";
//        System.out.println(Base64.encode(str));
//    }
}
