package com.trinasolar.integration.util;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigSaveReqVO;

import java.util.Set;

import static com.trinasolar.integration.constants.ErrorCodeConstants.PROJECT_CONFIG_INVALID;
import static com.trinasolar.tasc.framework.common.exception.util.ServiceExceptionUtil.exception;


public class ProjectConfigFormatValidateUtils {
    private static final String OAUTH_JSON = "{'client_id':'','client_secret':'','callback_url':'','access_token_expire_time':'','refresh_token_expire_time':''}";
    private static final String TOS_JSON = "{'endpoint':'','bucketName':'','accessKey':'','secretKey':''}";
    private static final String NACOS_JSON = "";

    public static void validateConfigContent(ProjectConfigSaveReqVO reqData) {
        if("OAUTH".equalsIgnoreCase(reqData.getConfigKey())){
            JSONObject formatJson = JSONUtil.parseObj(OAUTH_JSON);
            JSONObject submitJson = JSONUtil.parseObj(reqData.getConfigContent());
            Set<String> formatKeys = formatJson.keySet();
            Set<String> submitKeys = submitJson.keySet();
            compareSets(formatKeys,submitKeys);
            return;
        }
        if("TOS".equalsIgnoreCase(reqData.getConfigKey())){
            JSONObject formatJson = JSONUtil.parseObj(TOS_JSON);
            JSONObject submitJson = JSONUtil.parseObj(reqData.getConfigContent());
            Set<String> formatKeys = formatJson.keySet();
            Set<String> submitKeys = submitJson.keySet();
            compareSets(formatKeys,submitKeys);
            return;
        }
        throw exception(PROJECT_CONFIG_INVALID);
    }

    private static void compareSets(Set<String> formatKeys, Set<String> submitKeys) {
        if(formatKeys.size()!=submitKeys.size()){
            throw exception(PROJECT_CONFIG_INVALID);
        }
        if(!formatKeys.containsAll(submitKeys)&&submitKeys.containsAll(formatKeys)){
            throw exception(PROJECT_CONFIG_INVALID);
        }
    }
}
