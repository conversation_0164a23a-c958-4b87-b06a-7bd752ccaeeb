package com.trinasolar.integration.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.Map.Entry;


/**
 * http请求工具类
 */
@Slf4j
public class HttpClientUtil {


    /**
     * post请求
     */
    public static String doPost(String url) {
        return doPost(url, null, null);
    }

    public static String doPost(String url, Map<String, Object> params) {
        return doPost(url, params, null);
    }

    public static String doPost(String url, Map<String, Object> params, Map<String, String> header) {
        String body = null;
        try {
            // Post请求
            HttpPost httpPost = new HttpPost(url.trim());
            // 设置参数
            httpPost.setEntity(new UrlEncodedFormEntity(map2NameValuePairList(params), StandardCharsets.UTF_8));
            // 设置Header
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    httpPost.setHeader(new BasicHeader(entry.getKey(), entry.getValue()));
                }
            }
            // 发送请求,获取返回数据
            body = execute(httpPost);
        } catch (Exception e) {
            log.error("http请求发送失败：" + e.getMessage());
        }
        return body;
    }

    /**
     * postJson请求
     */
    public static String doPostJson(String url, Map<String, Object> params) {
        return doPostJson(url, params, null);
    }

    public static String doPostJson(String url, Map<String, Object> params, Map<String, String> header) {
        String json = null;
        if (params != null && !params.isEmpty()) {
            for (Iterator<Entry<String, Object>> it = params.entrySet().iterator(); it.hasNext(); ) {
                Entry<String, Object> entry = it.next();
                Object object = entry.getValue();
                if (object == null) {
                    it.remove();
                }
            }
            json = JSON.toJSONString(params);
        }
        return doPostJson(url, json, header);
    }

    public static String doPostJson(String url, String json) {
        return doPostJson(url, json, null);
    }

    public static String doPostJson(String url, String json, Map<String, String> header) {
        String body = null;
        try {
            // Post请求
            HttpPost httpPost = new HttpPost(url.trim());
            // 设置参数
            httpPost.setEntity(new StringEntity(json, ContentType.DEFAULT_TEXT.withCharset(StandardCharsets.UTF_8)));
            httpPost.setHeader(new BasicHeader("Content-Type", "application/json"));
            // 设置Header
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    httpPost.setHeader(new BasicHeader(entry.getKey(), entry.getValue()));
                }
            }
            // 发送请求,获取返回数据
            body = execute(httpPost);
        } catch (Exception e) {
            log.error("http请求发送失败：" + e.getMessage());
        }
        return body;
    }

    public static String doPutJson(String url, Map<String, Object> params, Map<String, String> header) {
        String json = null;
        if (params != null && !params.isEmpty()) {
            for (Iterator<Entry<String, Object>> it = params.entrySet().iterator(); it.hasNext(); ) {
                Entry<String, Object> entry = it.next();
                Object object = entry.getValue();
                if (object == null) {
                    it.remove();
                }
            }
            json = JSON.toJSONString(params);
        }
        return doPutJson(url, json, header);
    }

    public static String doPutJson(String url, String json, Map<String, String> header) {
        String body = null;
        try {
            // Post请求
            HttpPut httpPut = new HttpPut(url.trim());
            // 设置参数
            httpPut.setEntity(new StringEntity(json, ContentType.DEFAULT_TEXT.withCharset(StandardCharsets.UTF_8)));
            httpPut.setHeader(new BasicHeader("Content-Type", "application/json"));
            // 设置Header
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    httpPut.setHeader(new BasicHeader(entry.getKey(), entry.getValue()));
                }
            }
            // 发送请求,获取返回数据
            body = execute(httpPut);
        } catch (Exception e) {
            log.error("http请求发送失败：" + e.getMessage());
        }
        return body;
    }

    /**
     * get请求
     */
    public static String doGet(String url) {
        return doGet(url, null, null);
    }

    public static String doGet(String url, Map<String, String> header) {
        return doGet(url, null, header);
    }

    public static String doGet(String url, Map<String, Object> params, Map<String, String> header) {
        String body = null;
        try {
            // 1. 使用URIBuilder处理URL和参数，避免手动拼接错误
            URIBuilder uriBuilder = new URIBuilder(url.trim());
            // 2. 添加params中的查询参数（如果有）
            if (params != null && !params.isEmpty()) {
                Set<Entry<String, Object>> entrySet = params.entrySet();
                for (Map.Entry<String, Object> entry : entrySet) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    if (value != null) {
                        // 自动处理参数编码（支持中文、特殊字符）
                        uriBuilder.setParameter(key, value.toString());
                    }
                }
            }

            // 3. 构建最终URI
            URI uri = uriBuilder.build();
            HttpGet httpGet = new HttpGet(uri);
            // 4. 设置Header
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    httpGet.setHeader(new BasicHeader(entry.getKey(), entry.getValue()));
                }
            }
            // 5. 发送请求,获取返回数据
            body = execute(httpGet);
        } catch (Exception e) {
            log.error("http请求发送失败：" + e.getMessage());
        }
        return body;
    }

    /**
     * delete请求
     */
    public static String doDelete(String url) {
        return doDelete(url, null, null);
    }

    public static String doDelete(String url, Map<String, String> header) {
        return doDelete(url, null, header);
    }

    public static String doDelete(String url, Map<String, Object> params, Map<String, String> header) {
        String body = null;
        try {
            // Get请求
            HttpDelete httpDelete = new HttpDelete(url.trim());
            // 设置参数
            if (params != null && !params.isEmpty()) {
                String str = EntityUtils.toString(new UrlEncodedFormEntity(map2NameValuePairList(params), StandardCharsets.UTF_8));
                String uri = httpDelete.getURI().toString();
                if (uri.contains("?")) {
                    httpDelete.setURI(new URI(httpDelete.getURI().toString() + "&" + str));
                } else {
                    httpDelete.setURI(new URI(httpDelete.getURI().toString() + "?" + str));
                }
            }
            // 设置Header
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    httpDelete.setHeader(new BasicHeader(entry.getKey(), entry.getValue()));
                }
            }
            // 发送请求,获取返回数据
            body = execute(httpDelete);
        } catch (Exception e) {
            log.error("http请求发送失败：" + e.getMessage());
        }
        return body;
    }

    private static String execute(HttpRequestBase requestBase) throws Exception {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String body = null;
        try {
            try (CloseableHttpResponse response = httpclient.execute(requestBase)) {
                HttpEntity entity = response.getEntity();

                if (entity != null) {
                    body = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                }
                EntityUtils.consume(entity);
            } catch (Exception e) {
                log.error("http请求发送失败：" + e.getMessage());
            }
        } catch (Exception e) {
            log.error("http请求发送失败：" + e.getMessage());
        } finally {
            httpclient.close();
        }
        return body;
    }

    private static List<NameValuePair> map2NameValuePairList(Map<String, Object> params) {
        if (params != null && !params.isEmpty()) {
            List<NameValuePair> list = new ArrayList<>();
            for (String key : params.keySet()) {
                if (params.get(key) != null) {
                    String value = String.valueOf(params.get(key));
                    list.add(new BasicNameValuePair(key, value));
                }
            }
            return list;
        }
        return null;
    }

}

