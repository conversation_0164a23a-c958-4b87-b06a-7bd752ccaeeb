package com.trinasolar.integration.execption;

public class Pipe<PERSON>ineException extends RuntimeException{
    public PipeLineException() {
    }

    public PipeLineException(String message) {
        super(message);
    }

    public PipeLineException(String message, Throwable cause) {
        super(message, cause);
    }

    public PipeLineException(Throwable cause) {
        super(cause);
    }

    public PipeLineException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
