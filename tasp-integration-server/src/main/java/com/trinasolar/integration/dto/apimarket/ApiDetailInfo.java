package com.trinasolar.integration.dto.apimarket;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.integration.service.appmarket.SwaggerParserHandler;
import io.swagger.models.Swagger;
import io.swagger.parser.SwaggerParser;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ApiDetailInfo {
    /**
     * Api编码
     */
    private String code;

    /**
     * API id
     */
    private Long id;

    /**
     * API名称
     */
    private String name;

    /**
     * 发布组id
     */
    private Long pubOrgId;

    /**
     * 发布组名称
     */
    private String pubOrgName;

    /**
     * 分类id
     */
    private Long tagId;

    /**
     * 分类名称
     */
    private String tagName;

    /**
     * 版本号
     */
    private String version;

    /**
     * Json数据
     */
    private String apiSpec;

    /**
     * 试用网关地址
     */
    private List<String> gateway;

    /**
     * TSL-ClientID
     */
    private String sandboxClientId;

    /**
     * TSL-ClientSecret
     */
    private String sandboxClientSecret;

    /**
     * 解析后的字段
     */
    private List<ApiEndpoint> apiEndpoints;


    public ApiDetailInfo init() {
        if (StringUtils.isEmpty(apiSpec)) {
            return this;
        }
        SwaggerParserHandler parser = new SwaggerParserHandler(apiSpec, gateway);
        apiEndpoints = parser.parseEndpoints();
        return this;
    }
}
