package com.trinasolar.integration.dto.appLogs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SreLogQueryDTO implements Serializable {

    @ApiModelProperty(value = "应用系统Id")
    private Long sysId;

    @ApiModelProperty(value = "应用Id")
    private Long appId;

    @ApiModelProperty(value = "应用系统名称")
    private String sysName;

    @ApiModelProperty(value = "全名应用系统名称")
    private String namespaceSysName;

    @ApiModelProperty(value = "应用程序名称")
    private String appName;

    @ApiModelProperty(value = "查询关键字")
    private String searchKeyword;

    @ApiModelProperty(value = "查询区间")
    private String time;

    @ApiModelProperty(value = "页码")
    private Integer page;

    @ApiModelProperty(value = "页码")
    private Integer current;

    @ApiModelProperty(value = "每页大小")
    private Integer size;
}
