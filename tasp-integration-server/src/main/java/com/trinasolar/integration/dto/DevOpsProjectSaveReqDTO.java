package com.trinasolar.integration.dto;

import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *  管理后台 - DevOps项目管理保存的
 */
@Data
@ToString(callSuper = true)
public class DevOpsProjectSaveReqDTO {

    /**
     * 项目名称不能为空
     */
    private String projectName = "test";

    /**
     * 项目使用的模板ID
     */
    private String templateId = "100000000000002";

    /**
     * 项目英文名称不能为空
     */
    private String englishNameCustom = "";

    /**
     * 项目类型，固定2
     */
    private String typeId = "2";

    /**
     * 项目授权角色模板
     */
    private String roleTemplate = "200000000000005,200000000000006,200000000000007";

    /**
     * 项目拓展属性
     */
    private List<Map> props = new ArrayList<>();

    private String testModelId = "";

    private String issueId = "";

    /**
     * 项目管理员
     */
    private String administrator = "121100";

    /**
     * 项目所属部门
     */
    private String deptId = "";

    /**
     * 项目描述
     */
    private String description = "";

    /**
     * 父项目ID
     */
    private String parentCode = "";

    /**
     * 关联需求池
     */
    private String relationDemand = "";

    /**
     * 项目项目
     */
    private String relationProject = "";
}
