package com.trinasolar.integration.dto.pipeline;

import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class GrantPipelineDTO {

    // 用户ID列表
    private List<String> userList;

    // 操作权限码列表
    private List<String> actionCodes;


    public void grant() {
        actionCodes = new ArrayList<>(Arrays.asList("view", "list", "retry", "share", "download", "edit", "delete", "execute", "grant"));
    }

    public void revoke() {
        actionCodes = new ArrayList<>();
    }

}
