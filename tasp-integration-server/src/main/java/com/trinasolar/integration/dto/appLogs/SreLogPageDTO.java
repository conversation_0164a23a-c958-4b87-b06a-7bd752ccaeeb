package com.trinasolar.integration.dto.appLogs;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SreLogPageDTO implements Serializable {

    @ApiModelProperty(value = "页码")
    private Integer page;

    @ApiModelProperty(value = "每页大小")
    private Integer size;

    @ApiModelProperty(value = "当前页")
    private Integer current;

    @ApiModelProperty(value = "总页数")
    private Integer total;

    @ApiModelProperty(value = "记录")
    private List<JSONObject> records;

    @ApiModelProperty(value = "数据返回标志位")
    private boolean hasData;
}
