package com.trinasolar.integration.dto.pipeline;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class BuildParamsDTO {
    /**
     * 持续集成构建消息（保留字段）
     */
    @JSONField(name = "BK_CI_BUILD_MSG")
    private String bkCiBuildMsg;

    /**
     * DevOps平台中的团队标识
     */
    @JSONField(name = "TEAM_NAME")
    private String teamName;

    /**
     * DevOps平台项目唯一标识
     */
    @JSONField(name = "PROJ_NAME")
    private String projectName;

    /**
     * 部署目标应用标识
     */
    @JSONField(name = "APP_NAME")
    private String appName;

    /**
     * 代码库分支信息
     */
    @JSONField(name = "BRANCH")
    private BranchDTO branch;

    /**
     * 版本号模板
     */
    @JSONField(name = "VERSION")
    private String versionTemplate;

    /**
     * 容器内存限制（单位：MB）
     */
    @JSONField(name = "MEMORY")
    private String memoryLimit;

    /**
     * JVM内存配置
     */
    @JSONField(name = "JAR_MEM")
    private String jvmMemory;

    /**
     * 部署环境标识
     */
    @JSONField(name = "ENV")
    private String environment;

    /**
     * 镜像仓库地址
     */
    @JSONField(name = "CPACK_ADDR")
    private String cpackAddress;

    /**
     * 环境变量配置
     */
    @JSONField(name = "ENV_PROFILE")
    private String envProfile;

    /**
     * 流水线唯一标识
     */
    @JSONField(name = "pipelineId")
    private String pipelineId;
}
