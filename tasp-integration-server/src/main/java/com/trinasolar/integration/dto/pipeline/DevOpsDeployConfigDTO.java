package com.trinasolar.integration.dto.pipeline;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * DevOps部署配置传输对象
 */
@Data
@Accessors(chain = true)
public class DevOpsDeployConfigDTO {
    /**
     * 容器内应用程序的工作路径
     */
    private String workDir;

    /**
     * 要构建的Git分支
     */
    private String gitBranch;

    /**
     * Kubernetes部署的Pod副本数
     */
    private String replicas;

    /**
     * 高可用环境IP配置（保留字段）
     */
    private String halp;

    /**
     * 包含完整的流水线构建参数
     */
    private String data;
}


