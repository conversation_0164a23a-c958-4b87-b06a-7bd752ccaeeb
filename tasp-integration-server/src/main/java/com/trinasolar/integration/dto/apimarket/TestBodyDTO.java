package com.trinasolar.integration.dto.apimarket;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 测试请求体DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "测试请求体数据模型")
public class TestBodyDTO {

    @Schema(description = "名称", required = true, example = "测试名称")
    private String name;

    @Schema(description = "描述信息", example = "这是一个测试DTO")
    private String description;

    @Schema(description = "测试嵌套对象")
    private Test test;

    @Data
    @Schema(description = "测试嵌套对象模型")
    static class Test {
        @Schema(description = "年龄", example = "25")
        private String age;
        @Schema(description = "性别", example = "男")
        private String gender;
    }
}


