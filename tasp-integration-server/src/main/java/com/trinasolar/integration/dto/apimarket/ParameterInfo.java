package com.trinasolar.integration.dto.apimarket;


import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 参数信息（支持 body/header/query/path）
 * <AUTHOR>
 */
@Data
public class ParameterInfo {
    private String name;
    private String format;
    private String in;
    private String type;
    private String description;
    private List<String> enumValues;
    private boolean required;
    private List<String> schemaRequired;
    private String exampleValue;
    private String defaultValue;
    private JsonNode schema;
    private List<ParameterField> fields;
    private ParameterField item;
    private String reqBodyJson;
}
