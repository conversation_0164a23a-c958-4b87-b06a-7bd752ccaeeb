package com.trinasolar.integration.dto.pipeline;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PipelineBuildReqDTO {


    /**
     * 流水线唯一标识 - DevOps平台生成的UUID
     * 示例：3fa85f64-5717-4562-b3fc-2c963f66afa6
     */
    private String pipelineId;

    /**
     * 构建序号 - 每次构建自动递增的数字标识
     * 生成规则：时间戳末6位（避免超过Integer.MAX_VALUE）
     */
    private Integer buildNo;

    /**
     * 项目唯一标识 - 关联DevOps平台中的项目
     * 格式要求：项目编码（如：TC-HR-SYSTEM）
     */
    private String projectId;
}
