package com.trinasolar.integration.dto.product;

import lombok.Data;

/**
 * 产品信息数据传输对象
 * <p>
 * 用于在系统间传递产品相关数据
 *
 * <AUTHOR>
 */
@Data
public class ProductDTO {
    /**
     * 产品唯一标识ID
     */
    private Long id;

    /**
     * 产品相对ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 产品详细描述
     */
    private String description;

    /**
     * 产品相关URL链接
     */
    private String url;


    /**
     * 产品相关类别 用于跳转
     */
    private String type;

    /**
     * 产品供应商信息
     */
    private String supplier;

    /**
     * 产品分类
     */
    private String category;

    /**
     * 产品版本号
     */
    private String version;


    /**
     * logo url
     */
    private String logoUrl;
}
