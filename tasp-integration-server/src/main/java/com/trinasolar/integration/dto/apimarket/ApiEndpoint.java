package com.trinasolar.integration.dto.apimarket;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 接口端点信息
 *
 * <AUTHOR>
 */
@Data
public class ApiEndpoint {
    /**
     * 接口相对路径（不含basePath）
     */
    private String path;
    /**
     * 接口全路径
     */
    private List<String> fullPath;
    /**
     * 接口名称
     */
    private String name;
    /**
     * 接口描述
     */
    private String desc;
    private String httpMethod;
    private String consumes;
    private List<ParameterInfo> parameters;
    private List<ResponseInfo> response;

    public ApiEndpoint() {
        parameters = new ArrayList<>();
    }
}
