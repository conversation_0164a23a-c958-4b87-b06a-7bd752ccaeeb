package com.trinasolar.integration.dto.apimarket;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
@Data
public class UserDTO {
    private Long id;
    private String username;
    private String userRealname;
    private String userCode;
    private String createdTime;

    public UserDTO() {
        LocalDateTime now = LocalDateTime.now();
        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        createdTime = formatter.format(now);
    }

}
