package com.trinasolar.integration.dto.apimarket;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API响应实体
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiResponseDTO {
    /**
     * API编码
     */
    private String code;

    /**
     * API id
     */
    private Long id;

    /**
     * API名称
     */
    private String name;

    /**
     * 发布组id
     */
    private Long pubOrgId;

    /**
     * 发布组名称
     */
    private String pubOrgName;

    /**
     * 分类id
     */
    private Long tagId;

    /**
     * 分类名称
     */
    private String tagName;

    /**
     * 版本号
     */
    private String version;
}