package com.trinasolar.integration.dto.apimarket;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "文件上传请求DTO")
public class FileUploadDTO {
    @Schema(type = "string", format = "binary", description = "上传的文件", required = true)
    private MultipartFile file;

    @Schema(description = "上传用户名", example = "testUser", required = true, type = "string")
    private String username;
}