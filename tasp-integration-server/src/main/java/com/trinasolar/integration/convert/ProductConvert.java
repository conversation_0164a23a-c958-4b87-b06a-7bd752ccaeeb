package com.trinasolar.integration.convert;

import com.trinasolar.integration.api.entity.ProductServicePO;
import com.trinasolar.integration.dto.product.ProductDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface ProductConvert {

    ProductDTO toProductDTO(ProductServicePO product);

    List<ProductDTO> toProductDTOs(List<ProductServicePO> allProducts);
}