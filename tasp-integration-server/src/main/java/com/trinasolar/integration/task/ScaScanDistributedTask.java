package com.trinasolar.integration.task;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trinasolar.integration.api.dto.ScanTaskProgressDTO;
import com.trinasolar.integration.api.enums.CheckStatusEnum;
import com.trinasolar.integration.sca.entity.SCAScanTaskRecords;
import com.trinasolar.integration.service.SCAScanTaskRecordsService;
import com.trinasolar.integration.service.SCAService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Spring Boot分布式定时任务
 * 处理SCA扫描任务调度
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ScaScanDistributedTask {

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private SCAScanTaskRecordsService scanTaskRecordsService;

    @Autowired
    private SCAService scaService;


    /**
     * 每分钟执行一次（Spring原生@Scheduled注解）
     * cron表达式格式：秒 分 时 日 月 周 [年]
     */
    @Scheduled(cron = "0 * * * * ?")
    public void executeScaScan() {
        // 1. 获取分布式锁（使用项目统一Redis前缀）
        RLock taskLock = redissonClient.getLock("tasp:sca:scan:task:lock");

        try {
            // 2. 尝试获取锁（0秒等待，30秒自动释放，防止死锁）
            boolean isLocked = taskLock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                // 3. 执行任务逻辑（调用现有Service层方法）
                processPendingTasks();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 4. 确保锁释放（只释放当前线程持有的锁）
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
    }

    /**
     * 处理待执行的SCA扫描任务
     * 直接调用业务Service，符合分层架构
     */
    private void processPendingTasks() {
        log.info("=======执行的SCA状态扫描任务======");
        QueryWrapper<SCAScanTaskRecords> queryWrapper = new QueryWrapper<>();
        List<Integer> notFinishedCode = CheckStatusEnum.getNotFinishedCode();
        queryWrapper.in("check_status", notFinishedCode);
        List<SCAScanTaskRecords> pendingTasks = scanTaskRecordsService.list(queryWrapper);
        if (CollectionUtils.isEmpty(pendingTasks)) {
            return;
        }
        List<Long> taskIds = pendingTasks.stream().map(SCAScanTaskRecords::getTaskId).collect(Collectors.toList());
        List<ScanTaskProgressDTO> scanTaskProgressDTOS = new ArrayList<>();
        for (Long taskId : taskIds) {
            ScanTaskProgressDTO scanTaskProgressDTO = scaService.scanProgress(taskId);
            if (CheckStatusEnum.isFinished(scanTaskProgressDTO.getCheckStatus())) {
                scanTaskProgressDTOS.add(scanTaskProgressDTO);
            }
        }
        log.info(JSON.toJSONString(scanTaskProgressDTOS));
    }
}