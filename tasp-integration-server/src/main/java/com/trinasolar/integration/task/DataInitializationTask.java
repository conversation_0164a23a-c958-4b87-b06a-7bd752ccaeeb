package com.trinasolar.integration.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.integration.api.entity.AppSystem;
import com.trinasolar.integration.api.entity.ApplicationProgram;
import com.trinasolar.integration.api.entity.ApplicationProgramChange;
import com.trinasolar.integration.api.entity.InteAppSystemChange;
import com.trinasolar.integration.api.mapper.ApplicationProgramChangeMapper;
import com.trinasolar.integration.api.mapper.InteAppSystemChangeMapper;
import com.trinasolar.integration.dao.AppSystemMapper;
import com.trinasolar.integration.dao.ApplicationProgramMapper;
import com.trinasolar.tasc.framework.common.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @className: DataInitializationTask
 * @Description: 项目启动后执行数据初始化任务
 * 检查应用系统和应用程序变更表是否初始化，如果版本为0的记录为空，则查询全量数据并插入到变更表中
 * @author: pengshy
 * @date: 2025/9/3 
 */
@Component
@Slf4j
public class DataInitializationTask implements ApplicationRunner {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private InteAppSystemChangeMapper appSystemChangeMapper;

    @Autowired
    private ApplicationProgramChangeMapper appProgramChangeMapper;

    @Autowired
    private AppSystemMapper appSystemMapper;

    @Autowired
    private ApplicationProgramMapper appProgramMapper;

    private static final String SYSTEM_OPERATOR = "admin";
    private static final Integer INITIAL_VERSION = 0;
    private static final Integer CHANGE_TYPE_INSERT = 1;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始执行数据初始化任务...");
        
        // 使用分布式锁确保只有一个实例执行初始化
        RLock initLock = redissonClient.getLock("tasp:datashare:initialization:lock");
        
        try {
            // 尝试获取锁（10秒等待，60秒自动释放）
            boolean isLocked = initLock.tryLock(10, 60, TimeUnit.SECONDS);
            if (isLocked) {
                log.info("获取到初始化锁，开始执行数据初始化...");
                
                // 初始化应用系统变更表
                initializeAppSystemChangeTable();
                
                // 初始化应用程序变更表
                initializeApplicationProgramChangeTable();
                
                log.info("数据初始化任务执行完成");
            } else {
                log.info("未能获取到初始化锁，可能其他实例正在执行初始化任务");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("数据初始化任务被中断", e);
        } catch (Exception e) {
            log.error("数据初始化任务执行失败", e);
        } finally {
            // 确保锁释放
            if (initLock.isHeldByCurrentThread()) {
                initLock.unlock();
                log.info("释放初始化锁");
            }
        }
    }

    /**
     * 初始化应用系统变更表
     */
    private void initializeAppSystemChangeTable() {
        log.info("开始检查应用系统变更表初始化状态...");
        
        // 检查是否存在版本为0的记录
        LambdaQueryWrapper<InteAppSystemChange> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InteAppSystemChange::getCurrentVersion, INITIAL_VERSION);
        
        long count = appSystemChangeMapper.selectCount(queryWrapper);
        
        if (count > 0) {
            log.info("应用系统变更表已存在版本为0的记录，跳过初始化。记录数量: {}", count);
            return;
        }
        
        log.info("应用系统变更表中无版本为0的记录，开始初始化...");
        
        // 查询全量应用系统数据
        List<AppSystem> appSystems = appSystemMapper.selectList(new LambdaQueryWrapper<>());
        
        if (appSystems == null || appSystems.isEmpty()) {
            log.info("应用系统表中无数据，跳过初始化");
            return;
        }
        
        log.info("查询到应用系统数据 {} 条，开始插入变更表...", appSystems.size());
        
        LocalDateTime now = LocalDateTime.now();
        int successCount = 0;
        
        for (AppSystem appSystem : appSystems) {
            try {
                InteAppSystemChange change = InteAppSystemChange.builder()
                        .systemId(String.valueOf(appSystem.getId()))
                        .systemCode(appSystem.getEnSimpleName())
                        .changeType(CHANGE_TYPE_INSERT)
                        .currentVersion(INITIAL_VERSION)
                        .currentData(JsonUtils.toJsonString(appSystem))
                        .operator(SYSTEM_OPERATOR)
                        .changeTime(now)
                        .isProcessed(0)
                        .build();
                
                appSystemChangeMapper.insert(change);
                successCount++;
                
            } catch (Exception e) {
                log.error("插入应用系统变更记录失败，systemId: {}, systemCode: {}", 
                         appSystem.getId(), appSystem.getCode(), e);
            }
        }
        
        log.info("应用系统变更表初始化完成，成功插入 {} 条记录", successCount);
    }

    /**
     * 初始化应用程序变更表
     */
    private void initializeApplicationProgramChangeTable() {
        log.info("开始检查应用程序变更表初始化状态...");
        
        // 检查是否存在版本为0的记录
        LambdaQueryWrapper<ApplicationProgramChange> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApplicationProgramChange::getCurrentVersion, INITIAL_VERSION);
        
        long count = appProgramChangeMapper.selectCount(queryWrapper);
        
        if (count > 0) {
            log.info("应用程序变更表已存在版本为0的记录，跳过初始化。记录数量: {}", count);
            return;
        }
        
        log.info("应用程序变更表中无版本为0的记录，开始初始化...");
        
        // 查询全量应用程序数据
        List<ApplicationProgram> applicationPrograms = appProgramMapper.selectList(new LambdaQueryWrapper<>());
        
        if (applicationPrograms == null || applicationPrograms.isEmpty()) {
            log.info("应用程序表中无数据，跳过初始化");
            return;
        }
        
        log.info("查询到应用程序数据 {} 条，开始插入变更表...", applicationPrograms.size());
        
        LocalDateTime now = LocalDateTime.now();
        int successCount = 0;
        
        for (ApplicationProgram program : applicationPrograms) {
            try {
                ApplicationProgramChange change = ApplicationProgramChange.builder()
                        .appId(String.valueOf(program.getApplicationId()))
                        .appCode(program.getProgramNameEn())
                        .systemId(program.getSystemId())
                        .changeType(CHANGE_TYPE_INSERT)
                        .currentVersion(INITIAL_VERSION)
                        .currentData(JsonUtils.toJsonString(program))
                        .operator(SYSTEM_OPERATOR)
                        .changeTime(now)
                        .isProcessed(0)
                        .build();
                
                appProgramChangeMapper.insert(change);
                successCount++;
                
            } catch (Exception e) {
                log.error("插入应用程序变更记录失败，appId: {}, appCode: {}", 
                         program.getApplicationId(), program.getProgramCode(), e);
            }
        }
        
        log.info("应用程序变更表初始化完成，成功插入 {} 条记录", successCount);
    }
}
