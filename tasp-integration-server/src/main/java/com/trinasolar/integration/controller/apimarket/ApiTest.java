package com.trinasolar.integration.controller.apimarket;

import com.trinasolar.integration.dto.apimarket.FileUploadDTO;
import com.trinasolar.integration.dto.apimarket.FormLoginDTO;
import com.trinasolar.integration.dto.apimarket.TestBodyDTO;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Tag(name = "API TEST", description = "API测试控制器")
@RestController
@RequestMapping("/app-market-test")
public class ApiTest {

    @Operation(summary = "创建query参数接口", description = "通过GET请求传递query参数的示例接口")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "成功返回字符串结果", content = @Content(schema = @Schema(implementation = CommonResult.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误", content = @Content(schema = @Schema(implementation = CommonResult.class)))
    })
    @GetMapping("/api/query")
    public CommonResult<String> query(
            @Parameter(description = "查询关键词", required = true, example = "test")
            @RequestParam("query") String query) {
        return CommonResult.success("test get" + query);
    }

    @Operation(summary = "创建path参数接口", description = "通过GET请求传递path参数的示例接口")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "成功返回路径参数值", content = @Content(schema = @Schema(implementation = CommonResult.class))),
            @ApiResponse(responseCode = "400", description = "路径参数格式错误", content = @Content(schema = @Schema(implementation = CommonResult.class)))
    })
    @GetMapping("/api/path/{id}")
    public CommonResult<String> path(
            @Parameter(description = "资源唯一标识ID", required = true, example = "123456")
            @PathVariable("id") String id) {
        return CommonResult.success(id);
    }

    @PostMapping("/api/body")
    @Operation(
            summary = "处理JSON请求体数据",
            description = "接收并验证TestBodyDTO类型的JSON请求体，返回包含相同数据的响应",
            operationId = "handleRequestBody" // 显式指定operationId
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "请求成功",
                    content = @Content(schema = @Schema(implementation = TestBodyDTO.class))
            ),
            @ApiResponse(responseCode = "400", description = "请求参数错误")
    })
    public TestBodyDTO body(
            @io.swagger.v3.oas.annotations.parameters.RequestBody( // Swagger专用请求体注解
                    description = "测试请求体对象",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TestBodyDTO.class)
                    )
            )
            @org.springframework.web.bind.annotation.RequestBody TestBodyDTO body) { // Spring的请求体注解
        return body;
    }


    @Operation(
        summary = "文件上传接口", 
        description = "通过POST请求上传文件的示例接口",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "文件上传表单数据", 
            required = true, 
            content = @Content(
                mediaType = MediaType.MULTIPART_FORM_DATA_VALUE, 
                schema = @Schema(implementation = FileUploadDTO.class)
            )
        )
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "文件上传成功", content = @Content(schema = @Schema(implementation = CommonResult.class, subTypes = String.class))),
            @ApiResponse(responseCode = "400", description = "文件为空或参数缺失", content = @Content(schema = @Schema(implementation = CommonResult.class))),
            @ApiResponse(responseCode = "500", description = "文件上传失败", content = @Content(schema = @Schema(implementation = CommonResult.class)))
    })
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE) // 添加媒体类型
    public CommonResult<String> handleUpload(
            @ModelAttribute FileUploadDTO uploadDTO
    ) {
        return CommonResult.success(uploadDTO.getUsername() + "上传成功!" + uploadDTO.getFile().getOriginalFilename());
    }

    @Operation(summary = "表单提交接口", description = "通过POST请求传递form-urlencoded格式参数的示例接口", requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = MediaType.APPLICATION_FORM_URLENCODED_VALUE, schema = @Schema(implementation = FormLoginDTO.class))))
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "表单提交成功", content = @Content(schema = @Schema(implementation = CommonResult.class))),
            @ApiResponse(responseCode = "400", description = "表单参数缺失或格式错误", content = @Content(schema = @Schema(implementation = CommonResult.class)))
    })
    @PostMapping(value = "/urlencoded", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public CommonResult<String> url(
            @Parameter(hidden = true) @ModelAttribute FormLoginDTO formLogin
    ) {
        return CommonResult.success(formLogin.getUsername() + "," + formLogin.getPassword());
    }

}
