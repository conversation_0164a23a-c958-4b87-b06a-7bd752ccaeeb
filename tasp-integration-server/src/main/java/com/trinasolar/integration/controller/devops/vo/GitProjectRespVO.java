package com.trinasolar.integration.controller.devops.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - Git Project 的 VO")
@Data
@ToString(callSuper = true)
public class GitProjectRespVO {
    @Schema(description = "Git Project ID", example = "")
    private Long id;
    @Schema(description = "Git Project Name", example = "")
    private String name;
    @Schema(description = "Git Project Path", example = "")
    private String path;
    @Schema(description = "Git Project webUrl", example = "")
    private String webUrl;
}
