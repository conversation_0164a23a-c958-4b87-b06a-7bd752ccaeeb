package com.trinasolar.integration.controller.projectconfig.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;

import com.trinasolar.tasc.framework.common.core.DictFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 项目配置信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectConfigRespVO implements VO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2578")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "项目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15699")
    @ExcelProperty("项目ID")
    @Trans(type = TransType.SIMPLE,  fields = "name", ref = "projectName")
    private Long projectId;

    @Schema(description = "项目名", example = "test")
    @ExcelProperty("项目名称")
    private String projectName;

    @Schema(description = "配置名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("配置名称")
    private String configKey;

    @Schema(description = "配置内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("配置内容")
    private String configContent;

    @Schema(description = "配置名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("配置名称")
    private String configName;

    @Schema(description = "负责人", example = "赵六")
    @ExcelProperty("负责人")
    private String masterName;

    @Schema(description = "是否默认配置（0否 1是）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "是否默认配置（0否 1是）")
    private Integer isDefault;

    @Schema(description = "状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}