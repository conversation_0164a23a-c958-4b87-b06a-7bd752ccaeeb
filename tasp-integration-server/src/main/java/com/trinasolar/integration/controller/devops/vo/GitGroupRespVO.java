package com.trinasolar.integration.controller.devops.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - Git Group 的 VO")
@Data
@ToString(callSuper = true)
public class GitGroupRespVO {
    @Schema(description = "Git Group ID", example = "")
    private Long id;
    @Schema(description = "Git Group Name", example = "")
    private String name;
    @Schema(description = "Git Group Path", example = "")
    private String path;
    @Schema(description = "Git Web Url", example = "")
    private String webUrl;

}
