package com.trinasolar.integration.controller.component;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 共享组件管理表
 *
 * <AUTHOR>
 */
@Data
public class ShareComponentBO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 组件名称
     */
    @NotNull(message = "组件名称不能为空")
    @ApiModelProperty(value = "组件名称")
    private String componentName;

    /**
     * 版本号
     */
    @NotNull(message = "版本号不能为空")
    @ApiModelProperty(value = "版本号")
    private String version;

    /**
     * 组件描述
     */
    @NotNull(message = "组件描述不能为空")
    @ApiModelProperty(value = "组件描述")
    private String description;

    /**
     * 文件id
     */
    @NotNull(message = "文件不能为空")
    @ApiModelProperty(value = "文件id")
    private Long fileId;

    /**
     * 文件名称
     */
    @NotNull(message = "文件名称不能为空")
    private String fileName;

    /**
     * 文件url
     */
    @NotNull(message = "文件url不能为空")
    private String fileUrl;

    /**
     * logourl
     */
    @NotNull(message = "logourl不能为空")
    private String logoUrl;

    /**
     * 状态（0正常 1停用）
     */
    @ApiModelProperty(value = "状态（0正常 1停用）")
    private Integer status;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private String updater;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除：0：不删除，1：删除
     */
    private Integer deleted;
}
