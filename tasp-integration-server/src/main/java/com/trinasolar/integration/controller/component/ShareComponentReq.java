package com.trinasolar.integration.controller.component;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 共享组件管理表
 *
 * <AUTHOR>
 */
@Data
public class ShareComponentReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组件名称")
    private String componentName;

    @ApiModelProperty(value = "页数")
    private Integer page = 1;

    @ApiModelProperty(value = "分页大小")
    private Integer size = 10;
}
