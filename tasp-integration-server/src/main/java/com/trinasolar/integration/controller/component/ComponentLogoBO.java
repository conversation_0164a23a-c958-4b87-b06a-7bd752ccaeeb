package com.trinasolar.integration.controller.component;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 共享组件LOGO表
 *
 * <AUTHOR>
 */
@Data
public class ComponentLogoBO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件url
     */
    private String fileUrl;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除：0：不删除，1：删除
     */
    private Integer deleted;
}
