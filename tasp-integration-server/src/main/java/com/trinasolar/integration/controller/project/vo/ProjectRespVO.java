package com.trinasolar.integration.controller.project.vo;

import com.alibaba.excel.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 项目管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17363")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("项目名称")
    private String name;

    @Schema(description = "项目编码", requiredMode = Schema.RequiredMode.REQUIRED,  example = "Test")
    @ExcelProperty("项目编码")
    private String code;

    @Schema(description = "负责人", example = "赵六")
    @ExcelProperty("负责人")
    private String masterName;

    @Schema(description = "状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
//    @ExcelProperty(value = "状态（0正常 1停用）", converter = DictConvert.class)
//    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}