package com.trinasolar.integration.controller.projectconfig.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.trinasolar.tasc.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.trinasolar.tasc.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 项目配置信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProjectConfigPageReqVO extends PageParam {

    @Schema(description = "项目ID", example = "15699")
    @Trans(type = TransType.SIMPLE, fields = "name", ref = "projectName")
    private Long projectId;

    @Schema(description = "项目名", example = "test")
    private String projectName;

    @Schema(description = "配置名称")
    private String configKey;

    @Schema(description = "配置内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("配置内容")
    private String configContent;

    @Schema(description = "负责人", example = "赵六")
    private String masterName;

    @Schema(description = "状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}