package com.trinasolar.integration.controller.sca;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trinasolar.integration.api.dto.*;
import com.trinasolar.integration.service.SCAService;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/sca")
@Tag(name = "SCA签名服务", description = "提供SCA相关的签名和模块获取服务")
public class SCAController {

    @Resource
    private SCAService scaService;

    /**
     * 获取模块
     *
     * @return 模块信息
     */
    @GetMapping(value = "/module")
    @Operation(summary = "获取模块", description = "根据ID获取对应的SCA模块信息")
    public ScaResponseDTO<ScaDataDTO> getModules(
            @Parameter(description = "模块ID", required = true, example = "123")
            @RequestParam Long id) {
        return scaService.getModules(id);
    }

    @GetMapping(value = "/module/history")
    @Operation(summary = "获取应用程序的扫描历史记录", description = "根据ID获取对应的SCA模块信息")
    public CommonResult<SCAPage<ScaTaskDetailDTO>> getModulesScanHistory(
            @Parameter(description = "模块ID", required = true, example = "123")
            @RequestParam Long appId, @RequestParam Integer pageNo, @RequestParam Integer pageSize) {
        return CommonResult.success(scaService.getModulesScanHistory(appId, pageNo, pageSize));
    }

    /**
     * @param taskId  任务id
     * @param pattern 名称模糊查询
     * @param source  组件来源(0:全部, 1:官方, 2:非官方(自研))
     * @return
     */
    @GetMapping(value = "/comps/list/{task_id}")
    @Operation(summary = "扫描组件详情", description = "根据任务ID获取对应的扫描的组件列表信息")
    public CommonResult<SCAPage<ComponentInfo>> getCompsByTaskId(@PathVariable("task_id") Long taskId,
                                                                 @RequestParam String pattern, @RequestParam Integer source,
                                                                 @RequestParam Integer pageNo, @RequestParam Integer pageSize) {
        // 生成签名参数
        return CommonResult.success(scaService.getCompsByTaskId(taskId, pageNo, pageSize, pattern, source));
    }

    /**
     * @param taskId 任务id
     * @return
     */
    @GetMapping(value = "/result/{task_id}")
    @Operation(summary = "扫描组件详情", description = "根据任务ID获取对应的扫描的组件列表信息")
    public CommonResult<ScanTaskResultDTO> getScanResultByTaskId(@PathVariable("task_id") Long taskId) {
        // 生成签名参数
        return CommonResult.success(scaService.getCompsByTaskId(taskId));
    }

    /**
     * @param appId 应用程序id
     * @return
     */
    @GetMapping(value = "/scan/{id}")
    @Operation(summary = "扫描组件", description = "进行扫描")
    public CommonResult<ScanTaskDTO> scan(@PathVariable("id") Long appId) {
        return CommonResult.success(scaService.scan(appId));
    }
}
