package com.trinasolar.integration.controller.projectconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
@Accessors(chain = true)
@Schema(description = "管理后台 - 项目配置信息新增/修改 Request VO")
@Data
public class ProjectConfigSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2578")
    private Long id;

    @Schema(description = "项目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15699")
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @Schema(description = "配置名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "配置名称不能为空")
    private String configKey;

    @Schema(description = "配置内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "配置内容不能为空")
    private String configContent;

    private String configName;

    @Schema(description = "配置描述", example = "你猜")
    private String description;

    @Schema(description = "负责人", example = "赵六")
    private String masterName;

    @Schema(description = "状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态（0正常 1停用）不能为空")
    private Integer status;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}