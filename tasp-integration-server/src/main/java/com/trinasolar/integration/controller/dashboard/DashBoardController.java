package com.trinasolar.integration.controller.dashboard;

import com.trinasolar.integration.api.dto.PageResultDTO;
import com.trinasolar.integration.service.appmarket.ApiMarketService;
import com.trinasolar.integration.service.dashboard.DashBoardService;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.trinasolar.tasc.framework.common.pojo.CommonResult.success;

/**
 * 平台运营看板接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dashboard")
public class DashBoardController {

    @Autowired
    private DashBoardService dashBoardService;

    @Autowired
    private ApiMarketService apiMarketService;


    /**
     * 平台运营看板-代码质量概览 todo
     *
     * @param projectId 应用程序Id
     * @return
     */
    @GetMapping("/code/commit/{projectId}")
    public CommonResult<Object> getCodeQuality(@PathVariable Long projectId) {
        return success(dashBoardService.getCodeQuality(projectId));
    }

    @Operation(summary = "获取API总数接口", description = "获取API总数接口")
    @GetMapping("/api/count")
    public CommonResult<Long> getApiCount( @Parameter(description = "开始时间", in = ParameterIn.QUERY) @RequestParam(required = false, value = "startTime") String startTime,
                                           @Parameter(description = "结束时间", in = ParameterIn.QUERY) @RequestParam(required = false, value = "endTime") String endTime) {
        PageResultDTO apiCardList = apiMarketService.getApiCardList(null, null, null, startTime, endTime, null, 10, 1,
                null);
        return CommonResult.success(apiCardList.getTotal());
    }
}
