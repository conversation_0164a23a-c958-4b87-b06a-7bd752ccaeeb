package com.trinasolar.integration.controller;

import com.trinasolar.integration.api.entity.UserProductFavoritePO;
import com.trinasolar.integration.api.service.UserProductFavoriteService;
import com.trinasolar.integration.dto.appLogs.SreLogPageDTO;
import com.trinasolar.integration.dto.appLogs.SreLogQueryDTO;
import com.trinasolar.integration.dto.product.ProductRespDTO;
import com.trinasolar.integration.service.AppLogsService;
import com.trinasolar.integration.service.ProductServerService;
import com.trinasolar.integration.util.RequestUtils;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.trinasolar.tasc.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/product")
public class ProductController {

    @Resource
    private ProductServerService productServerService;

    @Resource
    private UserProductFavoriteService favoriteService;


    @GetMapping("/list")
    public CommonResult<ProductRespDTO> list() {
        return success(productServerService.list());
    }

    /**
     * 新增用户产品收藏
     *
     * @param productId 产品ID
     * @return 操作结果
     */
    @PostMapping("/add-favorite/{productId}")
    public CommonResult<Boolean> addFavorite(@PathVariable("productId") Long productId) {
        UserProductFavoritePO favorite = new UserProductFavoritePO()
                .setUserId(Long.parseLong(RequestUtils.getCurrentUser().getId()))
                .setProductId(productId);
        return CommonResult.success(favoriteService.save(favorite));
    }

    /**
     * 删除用户产品收藏
     *
     * @param productId 产品ID
     * @return 操作结果
     */
    @DeleteMapping("/remove-favorite/{productId}")
    public CommonResult<Boolean> removeFavorite(@PathVariable("productId") Long productId) {
        favoriteService.removeByUserAndProduct(Long.parseLong(RequestUtils.getCurrentUser().getId()), productId);
        return CommonResult.success(Boolean.TRUE);
    }


    @GetMapping("/component")
    public CommonResult<Long> getComponent() {
        return CommonResult.success(productServerService.getComponentCount());
    }

}
