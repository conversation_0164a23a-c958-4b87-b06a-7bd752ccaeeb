package com.trinasolar.integration.controller.devops.vo;

import cn.hutool.json.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - DevOps流水线构建日志 Response 的 VO")
@Data
@ToString(callSuper = true)
public class DevOpsPipelineBuildLogRespVO {

    private String buildId;
    private JSONArray logs;
    private String logString;

}
