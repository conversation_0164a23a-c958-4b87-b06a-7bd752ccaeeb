package com.trinasolar.integration.controller.devops.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - DevOps流水线 Response 的 VO")
@Data
@ToString(callSuper = true)
public class DevOpsPipelineRespVO {
    private String projectId;
    private String latestBuildId;
    private String pipelineId;
    private String pipelineName;
    private String latestBuildStatus;
    private long latestBuildStartTime;
    private long latestBuildEndTime;
    private String latestBuildUserId;
    private int latestBuildNum;
    private String lastBuildMsg;
    private int buildCount;
    private long createTime;
    private long updateTime;
}
