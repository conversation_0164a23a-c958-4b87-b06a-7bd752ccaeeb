package com.trinasolar.integration.controller;

import com.trinasolar.integration.api.entity.ApplicationProgram;
import com.trinasolar.integration.service.AppService;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 应用管理")
@RestController
@RequestMapping("/integration/app")
public class AppController {

    @Autowired
    AppService appService;

    /**
     * 创建应用程序流水线
     */
    @PostMapping("/create")
    public CommonResult<Boolean> createApp(@RequestBody ApplicationProgram applicationProgram) {
        appService.createApp(applicationProgram);
        return CommonResult.success(true);
    }

    @PostMapping("/crateSca/batch")
    public CommonResult<Boolean> createAppBatch(@RequestBody List<ApplicationProgram> applicationPrograms) {

        return CommonResult.success(appService.doProgramScaInitBatch(applicationPrograms));
    }

}
