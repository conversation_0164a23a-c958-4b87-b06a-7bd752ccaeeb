package com.trinasolar.integration.controller;

import cn.hutool.core.codec.Base64;
import com.trinasolar.integration.dto.appLogs.SreLogPageDTO;
import com.trinasolar.integration.dto.appLogs.SreLogQueryDTO;
import com.trinasolar.integration.service.AppLogsService;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 应用管理 - 应用日志")
@RestController
@RequestMapping("/app/log")
@Slf4j
public class AppLogController{

    @Autowired
    private AppLogsService appLogsService;

    /**
     * 查询应用日志
     * @param sreLogQueryDTO
     * @return
     */
    @PostMapping("/search")
    @Operation(summary = "查询应用日志")
    public CommonResult<SreLogPageDTO> searchAppLogs(@RequestBody SreLogQueryDTO sreLogQueryDTO) {
        return CommonResult.success(appLogsService.searchAppLogs(sreLogQueryDTO));
    }

    /**
     * 下载应用日志
     * @param sreLogQueryDTO
     * @return
     */
    @PostMapping("/download")
    @Operation(summary = "下载应用日志")
    public void downloadAppLogs(@RequestBody SreLogQueryDTO sreLogQueryDTO, HttpServletResponse response) {
        appLogsService.downloadAppLogs(sreLogQueryDTO, response);
    }
}
