package com.trinasolar.integration.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.integration.controller.component.ComponentLogoBO;
import com.trinasolar.integration.controller.component.ShareComponentBO;
import com.trinasolar.integration.controller.component.ShareComponentReq;
import com.trinasolar.integration.controller.component.ShareComponentVO;
import com.trinasolar.integration.service.ComponentLogoService;
import com.trinasolar.integration.service.ShareComponentService;
import com.trinasolar.tasc.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/share/component", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@Tag(name = "平台配置 - 共享组件管理")
public class ShareComponentController {

    @Autowired
    private ShareComponentService shareComponentService;

    @Autowired
    private ComponentLogoService componentLogoService;
    /**
     * 分页查询
     * @return
     */
    @PostMapping(value = "/page")
    @ApiOperation(value = "分页查询")
    public CommonResult<Page<ShareComponentVO>> shareComponentPage(Page<ShareComponentVO> page, @RequestBody ShareComponentReq req){
        return CommonResult.success(shareComponentService.shareComponentPage(page, req));
    }

    /**
     * 新增共享组件
     */
    @PostMapping
    @ApiOperation(value = "新增共享组件")
    public CommonResult<Boolean> addShareComponent(@RequestBody ShareComponentBO shareComponentBO) {
        // 校验组件名称是否重复（排除当前ID）
        if (shareComponentService.checkComponentNameExists(shareComponentBO.getComponentName(), null)) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(),"组件名称已存在，请更换");
        }
        if (!shareComponentService.addShareComponent(shareComponentBO)) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(),"添加失败");
        }
        return CommonResult.success(true);
    }

    /**
     * 更新共享组件
     */
    @PutMapping
    @ApiOperation(value = "更新共享组件")
    public CommonResult<Boolean> updateShareComponent(@RequestBody ShareComponentBO shareComponentBO) {
        // 校验组件名称是否重复（排除当前ID）
        if (shareComponentService.checkComponentNameExists(shareComponentBO.getComponentName(), shareComponentBO.getId())) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(),"组件名称已存在，请更换");
        }
        return CommonResult.success(shareComponentService.updateShareComponent(shareComponentBO));
    }

    /**
     * 删除共享组件
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除共享组件")
    public CommonResult<Boolean> deleteShareComponent(
            @ApiParam(value = "组件ID", required = true) @PathVariable Long id) {
        if (!shareComponentService.checkComponentNameExists(null, id)) {
            return CommonResult.error(GlobalErrorCodeConstants.NOT_FOUND.getCode(),"组件不存在");
        }
        return CommonResult.success(shareComponentService.deleteShareComponent(id));
    }

    /**
     * 上架，下架功能
     */
    @ApiOperation(value = "上架，下架功能")
    @PutMapping("/{id}/{status}")
    public CommonResult<Boolean> updateStatus(
            @ApiParam(value = "组件ID", required = true) @PathVariable Long id,
            @ApiParam(value = "状态", required = true) @PathVariable Integer status) {
        if (!shareComponentService.checkComponentNameExists(null, id)) {
            return CommonResult.error(GlobalErrorCodeConstants.NOT_FOUND.getCode(),"组件不存在");
        }
        return CommonResult.success(shareComponentService.updateStatus(id, status));
    }

    /**
     * 查询Logo
     * @return
     */
    @GetMapping(value = "/logo")
    @ApiOperation(value = "查询Logo")
    public CommonResult<List<ComponentLogoBO>> componentLogo(){
        return CommonResult.success(componentLogoService.componentLogo());
    }
}
