package com.trinasolar.integration.controller.devops.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DevOpsProjectAdminDTO {
    /**
     * 主键ID，唯一标识
     */
    private String id;

    /**
     * 用户ID，关联用户表
     */
    private String userId;

    /**
     * 用户名，显示用
     */
    private String username;

    /**
     * 范围ID，标识权限所属范围
     */
    private String scopeId;

    /**
     * 范围编码，如project/application等
     */
    private String scopeCode;

    /**
     * 创建人ID
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createdTime;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 更新人ID
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private String updatedTime;

    /**
     * 更新人姓名
     */
    private String updatedByName;
}
