package com.trinasolar.integration.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.integration.api.entity.ComponentLogoDO;
import com.trinasolar.integration.api.entity.ShareComponentDO;
import com.trinasolar.integration.controller.component.ComponentLogoBO;
import com.trinasolar.integration.controller.component.ShareComponentReq;
import com.trinasolar.integration.controller.component.ShareComponentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ComponentLogoMapper extends BaseMapper<ComponentLogoDO> {
    List<ComponentLogoBO> componentLogoList();
}
