package com.trinasolar.integration.dao;

import com.trinasolar.integration.api.entity.ProjectConfigDO;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigPageReqVO;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigSaveReqVO;
import com.trinasolar.integration.BaseMapperX;
import com.trinasolar.integration.LambdaQueryWrapperX;
import com.trinasolar.tasc.framework.common.pojo.PageResult;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目配置信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectConfigMapper extends BaseMapperX<ProjectConfigDO> {

    default PageResult<ProjectConfigDO> selectPage(ProjectConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProjectConfigDO>()
                .eqIfPresent(ProjectConfigDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ProjectConfigDO::getConfigKey, reqVO.getConfigKey())
                .likeIfPresent(ProjectConfigDO::getMasterName, reqVO.getMasterName())
                .eqIfPresent(ProjectConfigDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ProjectConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProjectConfigDO::getId));
    }

    default ProjectConfigDO selectByProjectIdAndConfigKey(ProjectConfigSaveReqVO createReqVO){
        return selectOne(new LambdaQueryWrapperX<ProjectConfigDO>()
                .eqIfPresent(ProjectConfigDO::getProjectId, createReqVO.getProjectId())
                .eqIfPresent(ProjectConfigDO::getConfigKey, createReqVO.getConfigKey())
                );
    }

    default List<ProjectConfigDO> selectByProjectId(Long projectId){
        return selectList(new LambdaQueryWrapperX<ProjectConfigDO>()
                .eqIfPresent(ProjectConfigDO::getProjectId, projectId)
        );
    }

    ProjectConfigDO selectProjectConfigByProjectId(Long sysId);
}
