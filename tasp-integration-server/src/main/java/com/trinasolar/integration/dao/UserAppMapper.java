package com.trinasolar.integration.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.integration.api.entity.UserApp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface UserAppMapper extends BaseMapper<UserApp> {

    @Select("select tasp_base.sys_user_app.*,tasp_base.sys_user.user_code from tasp_base.sys_user_app left join tasp_base.sys_user on tasp_base.sys_user_app.user_id = tasp_base.sys_user.id where app_id = #{appId}")
    List<UserApp> getUserAppByAppId(@Param("appId") Long appId);
}
