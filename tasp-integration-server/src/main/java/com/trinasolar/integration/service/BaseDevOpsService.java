package com.trinasolar.integration.service;

import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.google.common.collect.Maps;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

@Service
public class BaseDevOpsService {
    @Value("${trinasolar.devops.domain-host}")
    protected String DOMAIN_HOST; // DevOps根域名地址
    @Value("${trinasolar.devops.docker-img-host}")
    protected String DOCKER_IMG_DOMAIN_HOST; // DevOps根域名地址
    protected String CICD_TEMPLATE_PATH = "pipeline-templates/"; //流水线模板目录
    protected String CICD_DEV_BACK_TEMP_PATH = "[DEV]backend.json"; // 后端流水线开发环境模板
    protected String CICD_TEST_BACK_TEMP_PATH = "[TEST]backend.json"; // 后端流水线测试环境模板
    protected String CICD_UAT_BACK_TEMP_PATH = "[UAT]backend.json"; // 后端流水线UAT环境模板
    protected String CICD_PROD_BACK_TEMP_PATH = "[PROD]backend.json"; // 后端流水线生产环境模板

    protected String CICD_DEV_FRONT_TEMP_PATH = "[DEV]front.json"; // 后端流水线开发环境模板
    protected String CICD_TEST_FRONT_TEMP_PATH = "[TEST]front.json"; // 后端流水线测试环境模板
    protected String CICD_UAT_FRONT_TEMP_PATH = "[UAT]front.json"; // 后端流水线UAT环境模板
    protected String CICD_PROD_FRONT_TEMP_PATH = "[PROD]front.json"; // 后端流水线生产环境模板
    @Value("${trinasolar.devops.login-host:https://bizdevops-base.trinasolar.com}")
    private String LOGIN_DOMAIN_HOST = "https://bizdevops-base.trinasolar.com"; // DevOps登录认证的域名

    //缓存，默认1小时过期
    private final TimedCache<String, String> devopsCache = new TimedCache<>(1800000L);

    //    public static void main(String[] args) {
//        BaseDevOpsService baseDevOpsService = new BaseDevOpsService();
//        String sessionCookie = baseDevOpsService.getSessionCookie();
//        System.out.println(sessionCookie);
//
//    }
    public String getSessionCookie() {
        if (devopsCache.get("SESSION_COOKIE") != null) {
            return devopsCache.get("SESSION_COOKIE");
        }
        try {
            String url = LOGIN_DOMAIN_HOST + "/login/?c_url=https%3A%2F%2Fbizdevops.trinasolar.com%2Fconsole%2Fplatform%2Fentry&code=customLogin";
            Connection conn = Jsoup.connect(url);
            Connection.Response rs = conn.execute();
            Document doc = Jsoup.parse(rs.body());
            Element input = doc.select("input[name=csrfmiddlewaretoken]").first();
            String csrfmiddlewaretoken = input.attr("value");
            Element scriptElement = doc.select("script").last();
            String scriptContent = scriptElement.html();
            //获取PublicKey
            String publicKey = parseVariableValue(scriptContent, "PASSWORD_RSA_PUBLIC_KEY");
            //公钥加密
            RSA rsa = new RSA(null, Base64.decodeStr(publicKey)
                    .replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", ""));
            //密码必须使用RSA加密
            String encryPwd = rsa.encryptBase64("ts@123456", KeyType.PublicKey);

            //构建登录参数
            Map formData = buildFormMap(csrfmiddlewaretoken, encryPwd);
            Connection.Response login = Jsoup.connect(LOGIN_DOMAIN_HOST + "/login/?c_url=https%3A%2F%2Fbizdevops.trinasolar.com%2Fconsole%2Fplatform%2Fentry&code=customLogin")
                    .ignoreContentType(true)
                    .followRedirects(true)
//                    .header("Origin","https://bizdevops-base-test.trinasolar.com")
                    .header("Referer", LOGIN_DOMAIN_HOST + "/login/?c_url=https%3A%2F%2Fbizdevops.trinasolar.com%2Fconsole%2Fplatform%2Fentry&code=customLogin")
                    .data(formData)
                    .cookies(rs.cookies())
                    .method(Connection.Method.POST)
                    .execute()
                    .charset("utf-8");
            Map sessionCookieMap = login.cookies();
            final StringBuilder authCookie = new StringBuilder();
            sessionCookieMap.forEach((k, v) -> {
                authCookie.append(k + "=" + v + ";");
            });
            //必须加 X-DEVOPS-TENANT-ID=bk_ci;
            String resultCookie = authCookie.toString() + "X-DEVOPS-TENANT-ID=bk_ci;";
            devopsCache.put("SESSION_COOKIE", resultCookie);
            return resultCookie;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public String getSessionCookieByProjectCode(String projectCode) {
        return getSessionCookie() + "X-DEVOPS-PROJECT-ID=" + projectCode;
    }

    private static Map<String, String> buildFormMap(String csrf, String pwd) {
        Map<String, String> result = Maps.newHashMap();
        result.put("csrfmiddlewaretoken", csrf);
        result.put("app_id", "");
        result.put("next", "");
        result.put("username", "admin");
        result.put("password", pwd);
        return result;
    }

    /**
     * 提取页面Javascript中的某个变量值
     *
     * @param scriptContent js内容
     * @param variableName  匹配的变量名字
     * @return
     */
    private static String parseVariableValue(String scriptContent, String variableName) {
        String pattern = variableName + "\\s*=\\s*\"(.*?)\"";
        java.util.regex.Matcher matcher = java.util.regex.Pattern.compile(pattern).matcher(scriptContent);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
