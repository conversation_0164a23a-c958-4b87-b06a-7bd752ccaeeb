package com.trinasolar.integration.service;

import com.trinasolar.integration.api.entity.ProjectConfigDO;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigPageReqVO;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigSaveReqVO;
import com.trinasolar.tasc.framework.common.pojo.PageResult;


import javax.validation.Valid;

/**
 * 项目配置信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectConfigService {


    Boolean initCheck(Long appSystemId);
}