package com.trinasolar.integration.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.integration.service.SCAService;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR>
 */
public abstract class SCATokenService implements SCAService {

    private static final String DEFAULT_SIGNATURE_VERSION = "1.0";
    private static final String DEFAULT_SIGNATURE_METHOD = "HMAC-SHA256";
    private static final String SHA_256 = "SHA-256";


    /**
     * 访问密钥
     */
    @Value("${sca.access-key:68a52aa99d399eb54ed1d218}")
    private String accessKey;

    @Value("${sca.secret-key:12ce30bd-bd87-4f86-81e1-b3067f36ef31}")
    private String secretKey;


    /**
     * 生成并维护签名参数
     *
     * @param method        请求方式
     * @param payloadObject 请求体
     * @param requestParams 请求入参
     * @return 请求头参数map
     */
    @Override
    public Map<String, String> generateSignatureParams(String method, JSONObject payloadObject, Map<String, Object> requestParams) {
        Map<String, String> params = new HashMap<>();
        // 使用默认签名版本
        params.put("SignatureVersion", DEFAULT_SIGNATURE_VERSION);
        // 使用默认签名方法
        params.put("SignatureMethod", DEFAULT_SIGNATURE_METHOD);
        Long timestamp = Instant.now().getEpochSecond();
        // 生成当前时间戳
        params.put("Timestamp", String.valueOf(timestamp));
        // 生成随机Nonce
        String signatureNonce = UUID.randomUUID().toString();
        params.put("SignatureNonce", signatureNonce);
        // 设置访问密钥
        params.put("AccessKey", accessKey);
        params.put("Signature", getSignature(method, payloadObject, requestParams, timestamp));
        return params;
    }

    private String getSignature(String method, JSONObject payloadObject, Map<String, Object> requestParams, Long timestamp) {
        String body = "";
        if (payloadObject != null) {
            body = payloadObject.toJSONString();
        }
        // 对body进行sha256加密
        MessageDigest messageDigest = null;
        try {
            messageDigest = MessageDigest.getInstance(SHA_256);
            messageDigest.update(body.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 转成16进制
        String encodeBody = byte2Hex(messageDigest.digest());
        String query = spliceParam(requestParams);
        String s = timestamp + "\n" + method + "\n" + query + "\n" + encodeBody;
        return getHmacSHA256Value(secretKey, s);
    }

    private String byte2Hex(byte[] bytes) {
        StringBuilder stringBuffer = new StringBuilder();
        String temp;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    private String getHmacSHA256Value(String secret, String message) {
        try {
            Mac sha256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256.init(secretKey);
            byte[] hash = sha256.doFinal(message.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(hash);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 根据map的key进行字典升序排序
     *
     * @param map
     * @return map
     */
    private static Map<String, Object> sortMapByKey(Map<String, Object> map) {
        Map<String, Object> treemap = new TreeMap<>(map);
        List<Map.Entry<String, Object>> list = new ArrayList<>(treemap.entrySet());
        list.sort(Map.Entry.comparingByKey());
        return treemap;
    }

    private static String spliceParam(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }
        Map<String, Object> sortParams = sortMapByKey(params);
        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortParams.entrySet()) {
            stringBuilder.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8));
            stringBuilder.append("=");
            stringBuilder.append(URLEncoder.encode(entry.getValue().toString(), StandardCharsets.UTF_8));
            stringBuilder.append("&");
        }
        String query = "";
        if (stringBuilder.length() > 0) {
            query = stringBuilder.substring(0, stringBuilder.length() - 1);
        }
        return query;
    }
}