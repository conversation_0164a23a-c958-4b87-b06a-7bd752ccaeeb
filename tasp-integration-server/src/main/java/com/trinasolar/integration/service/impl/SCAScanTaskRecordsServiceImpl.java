package com.trinasolar.integration.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.integration.sca.entity.SCAScanTaskRecords;
import com.trinasolar.integration.dao.SCAScanTaskRecordsMapper;
import com.trinasolar.integration.service.SCAScanTaskRecordsService;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SCAScanTaskRecordsServiceImpl extends ServiceImpl<SCAScanTaskRecordsMapper, SCAScanTaskRecords> implements SCAScanTaskRecordsService {

    @Autowired
    private SCAScanTaskRecordsMapper scanTaskRecordsMapper;

   @Override
    public void executeScanTask(SCAScanTaskRecords task) {
        try {
            //task.setCheckStatus();
            this.updateById(task);
            log.info("SCA扫描任务完成: {}", task.getTaskId());
        } catch (Exception e) {
           // task.setCheckStatus("FAILED");
            this.updateById(task);
            log.error("SCA扫描任务失败: {}", task.getTaskId(), e);
        }
    }
}