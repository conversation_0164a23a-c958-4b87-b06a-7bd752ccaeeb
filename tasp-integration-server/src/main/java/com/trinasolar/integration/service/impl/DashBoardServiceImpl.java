package com.trinasolar.integration.service.impl;

import com.trinasolar.integration.service.dashboard.DashBoardService;
import com.trinasolar.tasc.framework.common.exception.DashboardException;
import org.springframework.stereotype.Service;

@Service
public class DashBoardServiceImpl implements DashBoardService {


    @Override
    public Object getCodeQuality(Long projectId) {
        if (projectId == null) {
            throw new DashboardException("应用程序ID不能为空！");
        }

        return null;
    }
}
