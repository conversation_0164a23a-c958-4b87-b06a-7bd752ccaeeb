package com.trinasolar.integration.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.trinasolar.integration.api.entity.ApplicationProgram;
import com.trinasolar.integration.api.entity.ProjectConfigDO;
import com.trinasolar.integration.api.vo.PipelineRespVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsPipelineBuildLogRespVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsPipelineHisRespVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsPipelineRespVO;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigSaveReqVO;
import com.trinasolar.integration.dao.AppSystemInitializeMapper;
import com.trinasolar.integration.dao.ApplicationProgramMapper;
import com.trinasolar.integration.dao.ProjectConfigMapper;
import com.trinasolar.integration.dto.pipeline.DevOpsDeployConfigDTO;
import com.trinasolar.integration.dto.pipeline.GrantPipelineDTO;
import com.trinasolar.integration.dto.pipeline.PrePipelineDTO;
import com.trinasolar.integration.execption.PipeLineException;
import com.trinasolar.integration.service.BaseDevOpsService;
import com.trinasolar.integration.service.PipelineService;
import com.trinasolar.tasc.framework.common.pojo.PageResult;
import com.trinasolar.tasc.framework.common.util.object.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.trinasolar.integration.util.ProjectConfigConstants.DEVOPS_BASIC_INFO_KEY;

@Slf4j
@Service
public class PipelineServiceImpl extends BaseDevOpsService implements PipelineService {

    @Autowired
    private ApplicationProgramMapper applicaitonProgramMapper;


    @Autowired
    private AppSystemInitializeMapper appSystemInitializeMapper;


    @Autowired
    @Qualifier("taskExecutor")
    ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private ProjectConfigMapper projectConfigMapper;


    private String getTemplatePath(String path) {
        log.warn("读取的相对路径: {}", path);

        // 检查路径是否为空
        if (StrUtil.isEmpty(path)) {
            log.error("模板路径为空");
            return "";
        }

        // 优先检查挂载路径
        String mountPath = "/pipeline-templates/" + path;
        File mountFile = new File(mountPath);
        if (mountFile.exists()) {
            log.info("成功从挂载路径加载模板文件: {}", mountPath);
            return mountPath;
        }

        // 回退到类路径加载
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(path)) {
            if (inputStream == null) {
                log.error("无法从类路径加载模板文件: {}", path);
                return "";
            }
            log.info("成功从类路径加载模板文件: {}", path);
            return path;
        } catch (IOException e) {
            log.error("读取模板文件失败: {}", path, e);
            return "";
        }
    }


    public String importPipeline(JSONObject envJson, String systemSimpleEnName,String programeNameEn, String devOpsCode,
                                 String env, String businessCode, String hashId,
                                 String appType, String sessionCookie) {
        String jsonTemp = "";
        String pipeLineId = "";
        Map<String, String> envMap = new HashMap<>();
        envMap.put("DEV", "develop");
        envMap.put("TEST", "release");
        envMap.put("UAT", "release");
        envMap.put("PROD", "master");
        //  String branch = "master"; // todo 写死
        String workDir = "/";// todo 待确定和项目
        if ("DEV".equalsIgnoreCase(env)) {
            if ("后端".equalsIgnoreCase(appType)|| "endback".equalsIgnoreCase(appType)) {
                jsonTemp = CICD_TEMPLATE_PATH + CICD_DEV_BACK_TEMP_PATH;
            } else {
                jsonTemp = CICD_TEMPLATE_PATH + CICD_DEV_FRONT_TEMP_PATH;
            }
            jsonTemp = getTemplatePath(jsonTemp);
            JSONObject json = JSONUtil.readJSONObject(new File(jsonTemp), CharsetUtil.CHARSET_UTF_8);
            String replaceStr = replaceJsonString(json, devOpsCode, hashId, programeNameEn, envMap.get(env), systemSimpleEnName, businessCode, workDir);
            FileUtil.writeString(replaceStr, new File(jsonTemp + ".new"), CharsetUtil.CHARSET_UTF_8);
            pipeLineId = doInitCicdPipeline(devOpsCode, jsonTemp + ".new", sessionCookie, env, programeNameEn);
            addProjectToEnvGroup(env, envJson, pipeLineId, devOpsCode, sessionCookie);
        } else if ("TEST".equalsIgnoreCase(env)) {
            if ("后端".equalsIgnoreCase(appType)|| "endback".equalsIgnoreCase(appType)) {
                jsonTemp = CICD_TEMPLATE_PATH + CICD_TEST_BACK_TEMP_PATH;
            } else {
                jsonTemp = CICD_TEMPLATE_PATH + CICD_TEST_FRONT_TEMP_PATH;
            }
            InputStream templateStream = getClass().getClassLoader().getResourceAsStream(jsonTemp);
            JSONObject json = JSONUtil.parseObj(templateStream);
            String replaceStr = replaceJsonString(json, devOpsCode, hashId, programeNameEn, envMap.get(env), systemSimpleEnName, businessCode, workDir);
            // TODO 需要将新文件放到一个新的地方，加.new是为了测试
            FileUtil.writeString(replaceStr, new File(jsonTemp + ".new"), CharsetUtil.CHARSET_UTF_8);
            pipeLineId = doInitCicdPipeline(devOpsCode, jsonTemp + ".new", sessionCookie, env, programeNameEn);
            addProjectToEnvGroup(env, envJson, pipeLineId, devOpsCode, sessionCookie);
        } else if ("UAT".equalsIgnoreCase(env)) {
            if ("后端".equalsIgnoreCase(appType)|| "endback".equalsIgnoreCase(appType)) {
                jsonTemp = CICD_TEMPLATE_PATH + CICD_UAT_BACK_TEMP_PATH;
            } else {
                jsonTemp = CICD_TEMPLATE_PATH + CICD_UAT_FRONT_TEMP_PATH;
            }
            jsonTemp = getTemplatePath(jsonTemp);
            JSONObject json = JSONUtil.readJSONObject(new File(jsonTemp), CharsetUtil.CHARSET_UTF_8);
            String replaceStr = replaceJsonString(json, devOpsCode, hashId, programeNameEn, envMap.get(env), systemSimpleEnName, businessCode, workDir);
            // TODO 需要将新文件放到一个新的地方，加.new是为了测试
            FileUtil.writeString(replaceStr, new File(jsonTemp + ".new"), CharsetUtil.CHARSET_UTF_8);
            pipeLineId = doInitCicdPipeline(devOpsCode, jsonTemp + ".new", sessionCookie, env, programeNameEn);
            addProjectToEnvGroup(env, envJson, pipeLineId, devOpsCode, sessionCookie);
        } else if ("PROD".equalsIgnoreCase(env)) {
            if ("后端".equalsIgnoreCase(appType)|| "endback".equalsIgnoreCase(appType)) {
                jsonTemp = CICD_TEMPLATE_PATH + CICD_PROD_BACK_TEMP_PATH;
            } else if ("endback".equalsIgnoreCase(appType)) {
                jsonTemp = CICD_TEMPLATE_PATH + CICD_PROD_BACK_TEMP_PATH;
            } else {
                jsonTemp = CICD_TEMPLATE_PATH + CICD_PROD_FRONT_TEMP_PATH;
            }
            jsonTemp = getTemplatePath(jsonTemp);
            JSONObject json = JSONUtil.readJSONObject(new File(jsonTemp), CharsetUtil.CHARSET_UTF_8);
            String replaceStr = replaceJsonString(json, devOpsCode, hashId, programeNameEn, envMap.get(env), systemSimpleEnName, businessCode, workDir);
            // TODO 需要将新文件放到一个新的地方，加.new是为了测试
            FileUtil.writeString(replaceStr, new File(jsonTemp + ".new"), CharsetUtil.CHARSET_UTF_8);
            pipeLineId = doInitCicdPipeline(devOpsCode, jsonTemp + ".new", sessionCookie, env, programeNameEn);
            addProjectToEnvGroup(env, envJson, pipeLineId, devOpsCode, sessionCookie);
        }
        return pipeLineId;
    }


    /**
     * 将流水线与环境分组信息绑定
     *
     * @param env
     * @param envJson
     * @param pipeLineId
     * @param projectCode
     * @param cookies
     */
    private void addProjectToEnvGroup(String env, JSONObject envJson, String pipeLineId, String projectCode, String cookies) {
        String addProjToEnvGroupUrl = DOMAIN_HOST + "/ms/process/api/user/pipelineViews/projects/" + projectCode + "/bulkAdd";
        JSONArray pipelineIdsAry = new JSONArray();
        pipelineIdsAry.add(pipeLineId);
        JSONObject formData = new JSONObject();
        formData.putOnce("pipelineIds", pipelineIdsAry);
        JSONArray viewIdsAry = new JSONArray();
        viewIdsAry.add(envJson.getStr(env));
        formData.putOnce("viewIds", viewIdsAry);
        HttpResponse resp = HttpRequest.post(addProjToEnvGroupUrl)
                .cookie(cookies)
                .contentType("application/json")
                .body(JSONUtil.toJsonStr(formData)).execute();
        if (resp.getStatus() == 200) {
            String result = resp.body();
            if (!StrUtil.isEmpty(result)) {
                JSONObject json = JSONUtil.parseObj(result);
                if (json.getInt("status") == 0) {
                    log.info("流水线转移" + env + "分组成功!");
                }
            }
        }

    }

    /**
     * 生成流水线
     *
     * @param projectCode
     * @param jsonTemp
     * @param sessionCookie
     * @param env
     * @param appName
     * @return
     */
    private String doInitCicdPipeline(String projectCode, String jsonTemp, String sessionCookie, String env, String appName) {
        HttpResponse response = HttpRequest.post(DOMAIN_HOST + "/ms/process/api/user/pipelinesIO/[projectCode]/json".replace("[projectCode]", projectCode))
                .cookie(sessionCookie)
                .form("file", new File(jsonTemp)).timeout(10000)//超时，毫秒
                .execute();
        String searchPipelineUrl = DOMAIN_HOST + "/ms/process/api/user/pipelines/projects/" + projectCode + "/listViewPipelines?showDelete=true&sortType=CREATE_TIME&collation=DESC&page=1&pageSize=50&refresh=true&filterByPipelineName=[pipelineName]&viewId=unclassified";
        String pipeLineId = "";
        String result = response.body();
        log.info("Result：" + result);
        if (response.getStatus() == 200) {
            if (!StrUtil.isEmpty(result)) {
                JSONObject json = JSONUtil.parseObj(result);
                if (json.getInt("status") == 0) {
                    //获取流水线ID
                    HttpResponse searchPipelineResponse = HttpRequest.get(searchPipelineUrl.replace("[pipelineName]", "[" + env + "]" + appName))
                            .cookie(sessionCookie)
                            .execute();
                    if (searchPipelineResponse.getStatus() == 200) {
                        String searchResult = searchPipelineResponse.body();
                        if (!StrUtil.isEmpty(searchResult)) {
                            JSONObject sjson = JSONUtil.parseObj(searchResult);
                            if (sjson.getInt("status") == 0) {
                                JSONObject sdata = sjson.getJSONObject("data");
                                JSONArray records = sdata.getJSONArray("records");
                                if (!records.isEmpty()) {
                                    pipeLineId = records.getJSONObject(0).getStr("pipelineId");
                                }
                            }
                        }
                    }
                }
            }
        }
        log.info("pipelineID:" + pipeLineId);
        return pipeLineId;
    }

    private String replaceJsonString(JSONObject json,
                                     String devOpsCode,
                                     String gitHashId,
                                     String programeNameEn,
                                     String branch,
                                     String systemSimpleEnName,
                                     String businessCode,
                                     String workDir) {
        return JSONUtil.toJsonStr(json).replace("[APP_NAME]", programeNameEn)
                .replace("[PROJECT_NAME]", systemSimpleEnName)
                .replace("[BRANCH_NAME]", branch)
                .replace("[PROJECT_ID]", devOpsCode)
                .replace("[GIT_HASH_ID]", gitHashId)
                .replace("[TEAM_NAME]", businessCode)
                .replace("[WORK_DIR]", workDir);
    }

    private String getWorkDir4Cloud(String appName, String moduleName, String workdir) {
        if ("gateway".equalsIgnoreCase(moduleName)) {
            return "/" + appName + "-gateway";
        } else if ("system".equalsIgnoreCase(moduleName)) {
            return "/" + appName + "-module-system/" + appName + "-module-system-biz";
        } else if ("infra".equalsIgnoreCase(moduleName)) {
            return "/" + appName + "-module-infra/" + appName + "-module-infra-biz";
        } else {
            return workdir;
        }
    }


    /**
     * 将 Date 类型的时间转换为 yyyy-MM-dd HH:mm:ss 格式的字符串
     *
     * @param timestamp 待转换的时间戳
     * @return 格式化后的日期字符串，如果 timestamp 为 null 则返回 null
     */
    private String formatDate(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date(timestamp));
    }

    @Override
    public PageResult<PipelineRespVO> getPipelines(Long programId) {
        // Convert programId to Long directly instead of String
        ApplicationProgram applicationProgram = applicaitonProgramMapper.selectById(programId);
        Long applicationId = applicationProgram.getApplicationId();
        //获取projectId
        String projectCode = getProjectCode(applicationId);
//        QueryWrapper<AppSystemInitialize> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("app_id", applicationId);
//        queryWrapper.eq("relation_type", RelationTypeConstant.DVEOPS);
//        AppSystemInitialize appSystemInitialize = appSystemInitializeMapper.selectOne(queryWrapper);
//        String  projectCode= appSystemInitialize.getRelationId();

        //获取appName
        String appName = applicationProgram.getProgramNameEn();
        //请求BizDevOps
        String reqUrl = DOMAIN_HOST + "/ms/process/api/user/pipelines/projects/" + projectCode + "/listViewPipelines?showDelete=true&sortType=LAST_EXEC_TIME&collation=DESC&page=1&pageSize=100&filterByPipelineName=]" + appName + "&viewId=allPipeline";
        HttpResponse searchResp = HttpRequest.get(reqUrl).cookie(getSessionCookie()).execute();
        if (searchResp.getStatus() == 200) {
            String searchData = searchResp.body();
            //  log.info("流水线信息 searchData:" + searchData);
            log.info("流水线信息getPipelines 查询成功");
            if (!StrUtil.isEmpty(searchData)) {
                JSONObject json = JSONUtil.parseObj(searchData);
                if (json.getInt("status") == 0) {
                    int count = json.getJSONObject("data").getInt("count");
                    if (count > 0) {
                        JSONArray result = json.getJSONObject("data").getJSONArray("records");
                        List<DevOpsPipelineRespVO> pipelines = BeanUtils.toBean(result, DevOpsPipelineRespVO.class);
                        // 过滤掉非[DEV][PROD][UAT][TEST]
                        List<String> pipelineNames = List.of("[DEV]" + appName, "[PROD]" + appName, "[UAT]" + appName, "[TEST]" + appName,
                                "[dev]" + appName, "[prod]" + appName, "[uat]" + appName, "[test]" + appName);
                        pipelines = pipelines.stream().filter(e -> pipelineNames.contains(e.getPipelineName())).collect(Collectors.toList());
                        List<PipelineRespVO> pipelineRespVOs = new ArrayList<>();
                        for (DevOpsPipelineRespVO pipeline : pipelines) {
                            PipelineRespVO pipelineRespVO = new PipelineRespVO();
                            pipelineRespVO.setPipelineId(pipeline.getPipelineId());
                            pipelineRespVO.setProjectCode(projectCode);
                            pipelineRespVO.setProgramId(programId);
                            pipelineRespVO.setPipelineName(pipeline.getPipelineName());
                            pipelineRespVO.setEnvName(getEnvName(pipeline.getPipelineName()));
                            pipelineRespVO.setLatestBuildStatus(pipeline.getLatestBuildStatus());
                            pipelineRespVO.setLatestBuildStartTime(formatDate(pipeline.getLatestBuildStartTime()));
                            pipelineRespVO.setBuildUser(pipeline.getLatestBuildUserId());
                            pipelineRespVO.setLatestBuildId(pipeline.getLatestBuildId());
                            pipelineRespVO.setLatestBuildEndTime(formatDate(pipeline.getLatestBuildEndTime()));
                            pipelineRespVO.setLatestBuildNum(pipeline.getLatestBuildNum());
                            pipelineRespVO.setLastBuildMsg(pipeline.getLastBuildMsg());
                            pipelineRespVO.setBuildCount(pipeline.getBuildCount());
                            pipelineRespVO.setCreateTime(formatDate(pipeline.getCreateTime()));
                            pipelineRespVO.setUpdateTime(formatDate(pipeline.getUpdateTime()));
                            pipelineRespVOs.add(pipelineRespVO);
                        }
                        return new PageResult<>(pipelineRespVOs, Long.valueOf(pipelineRespVOs.size()));
                    }
                }
            }
        }
        return null;
    }

    private static String getEnvName(String pipelineName) {
        Pattern pattern = Pattern.compile("\\[(.*?)]");
        Matcher matcher = pattern.matcher(pipelineName);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "DEV";
    }

    private String getProjectCode(Long applicationId) {
        ProjectConfigSaveReqVO reqVO = new ProjectConfigSaveReqVO();
        reqVO.setProjectId(applicationId);
        reqVO.setConfigKey(DEVOPS_BASIC_INFO_KEY);
        ProjectConfigDO projectConfigDO = projectConfigMapper.selectByProjectIdAndConfigKey(reqVO);
        JSONObject proJson = JSONUtil.parseObj(projectConfigDO.getConfigContent());
        return proJson.getStr("projectCode");
    }


    public JSONObject getPipeLineParams(String projectId, String pipelineId) {
        String pipeLineUrl = DOMAIN_HOST + "/ms/process/api/user/builds/" + projectId + "/" + pipelineId + "/manualStartupInfo";
        log.info("pipeLineUrl:***********" + pipeLineUrl);
        String sessionCookie = getSessionCookie();
        HttpResponse searchResp = HttpRequest.get(pipeLineUrl).cookie(sessionCookie).execute();
        if (searchResp.getStatus() == 200) {
            String searchData = searchResp.body();
            log.info("getPipeLineParams 成功");
            if (!StrUtil.isEmpty(searchData)) {
                JSONObject json = JSONUtil.parseObj(searchData);
                if (json.getInt("status") == 0) {
                    return json.getJSONObject("data");
                }
            }
        }
        return null;
    }

    @Override
    public JSONObject deploy(Long projectId, String pipelineId) {
        String projectCode = getProjectCode(projectId);
        JSONObject pipeLineParams = getPipeLineParams(projectCode, pipelineId);
        Integer buildNo = pipeLineParams.getJSONObject("buildNo").getInt("buildNo");
        JSONArray properties = pipeLineParams.getJSONArray("properties");
        List<PrePipelineDTO> prePipelineDTOS = JSONUtil.toList(properties, PrePipelineDTO.class);
        String workDir = null;
        String gitBranch = "master";
        String replicas = "";
        String halp = "";
        String data = "";
        Map<String, Object> propertiesMap = new HashMap<>();
        for (PrePipelineDTO e : prePipelineDTOS) {
            propertiesMap.put(e.getId(), e.getDefaultValue());
            if ("WORK_DIR".equalsIgnoreCase(e.getId())) {
                workDir = String.valueOf(e.getDefaultValue());
                continue;
            }
            if ("REPLICAS".equalsIgnoreCase(e.getId())) {
                replicas = String.valueOf(e.getDefaultValue());
                continue;
            }
            if ("halp".equalsIgnoreCase(e.getId())) {
                halp = String.valueOf(e.getDefaultValue());
            }
        }
        propertiesMap.put("userId", "121100");
        propertiesMap.put("triggerUser", "zhm");
        data = JSONUtil.toJsonStr(propertiesMap);
        DevOpsDeployConfigDTO deployConfigDTO = new DevOpsDeployConfigDTO();
        deployConfigDTO.setHalp(halp).setWorkDir(workDir).setReplicas(replicas).setGitBranch(gitBranch).setData(data);
        String doDeployUrl = DOMAIN_HOST + "/ms/process/api/user/builds/" + projectCode + "/" + pipelineId + "?buildNo=" + buildNo;
        HttpResponse deployResp = HttpRequest.post(doDeployUrl)
                .cookie(getSessionCookie())
                .contentType("application/json")
                .body(JSONUtil.toJsonStr(data)).execute();
        if (deployResp.getStatus() == 200) {
            String searchData = deployResp.body();
            if (!StrUtil.isEmpty(searchData)) {
                JSONObject json = JSONUtil.parseObj(searchData);
                if (json.getInt("status") == 0) {
                    return json.getJSONObject("data");
                }
            }
        }
        throw new PipeLineException("执行流水线异常");
    }

    @Override
    public Boolean grant(Long projectId, String appName, List<String> userCodes) {
        GrantPipelineDTO grantPipelineDTO = new GrantPipelineDTO();
        grantPipelineDTO.setUserList(userCodes);
        grantPipelineDTO.grant();
        return modifyUserToPipeline(projectId, appName, grantPipelineDTO);
    }

    private boolean modifyUserToPipeline(Long projectId, String appName, GrantPipelineDTO grantPipelineDTO) {
        String projectCode = getProjectCode(projectId);
        String reqUrl = DOMAIN_HOST + "/ms/process/api/user/pipelines/projects/" + projectCode + "/listViewPipelines?showDelete=true&sortType=LAST_EXEC_TIME&collation=DESC&page=1&pageSize=100&filterByPipelineName=]" + appName + "&viewId=allPipeline";
        List<String> pipelineIds = new ArrayList<>();
        try (HttpResponse searchResp = HttpRequest.get(reqUrl).cookie(getSessionCookie()).execute()) {
            String searchData = searchResp.body();
            JSONObject json = JSONUtil.parseObj(searchData);
            if (json.getInt("status") == 0) {
                int count = json.getJSONObject("data").getInt("count");
                if (count > 0) {
                    JSONArray result = json.getJSONObject("data").getJSONArray("records");
                    List<DevOpsPipelineRespVO> pipelines = BeanUtils.toBean(result, DevOpsPipelineRespVO.class);
                    pipelineIds = pipelines.stream().map(DevOpsPipelineRespVO::getPipelineId).collect(Collectors.toList());
                }
            }
        }
        if (CollectionUtils.isEmpty(pipelineIds)) {
            throw new PipeLineException("未查询到相关流水线信息");
        }
        // 4个环境都应该要添加用户权限
        CountDownLatch countDownLatch = new CountDownLatch(pipelineIds.size());
        AtomicReference<Boolean> isSuccess = new AtomicReference<>(true);
        for (String pipelineId : pipelineIds) {
            taskExecutor.execute(() -> {
                try {
                    modifyUserToPipeline(grantPipelineDTO, projectCode, pipelineId);
                } catch (Exception e) {
                    isSuccess.set(false);
                    log.error("流水线:{}修改用户异常！", pipelineId);
                    throw new PipeLineException(e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("异步查询流水线异常！");
            throw new PipeLineException(e);
        }
        //
        if (!isSuccess.get()) {
            log.error("异步查询流水线异常！请检查详细日志！");
            throw new PipeLineException();
        }
        return true;
    }

    @Override
    public Boolean revoke(Long projectId, String appName, List<String> userCodes) {
        GrantPipelineDTO grantPipelineDTO = new GrantPipelineDTO();
        grantPipelineDTO.setUserList(userCodes);
        grantPipelineDTO.revoke();
        return modifyUserToPipeline(projectId, appName, grantPipelineDTO);
    }

    /**
     * 目前接口单条流水线添加多个用户
     */
    private void modifyUserToPipeline(GrantPipelineDTO grantPipelineDTO, String projectCode, String pipelineId) {
        String doDeployUrl = DOMAIN_HOST + "/ms/process/api/user/pipelines/cw/" + pipelineId + "/grant";
        String sessionCookie = getSessionCookie() + "X-DEVOPS-PROJECT-ID=" + projectCode;
        try (HttpResponse deployResp = HttpRequest.post(doDeployUrl)
                .cookie(sessionCookie)
                .contentType("application/json")
                .body(JSONUtil.toJsonStr(grantPipelineDTO)).execute()) {
            if (deployResp.getStatus() != 200 || StrUtil.isEmpty(deployResp.body())) {
                log.error("异步查询流水线异常！调用devops接口异常！");
                throw new PipeLineException("异步查询流水线异常！调用devops接口异常！");
            }
        }
    }

    @Override
    public PageResult<DevOpsPipelineHisRespVO> getPipelinesHistory(String projectId, String pipelineId) {
        //只取该流水线最近的10次构建历史，取多了也没有用
        String reqUrl = DOMAIN_HOST + "/ms/process/api/user/builds/" + projectId + "/" + pipelineId + "/history/new?page=1&pageSize=10&";
        HttpResponse searchResp = HttpRequest.get(reqUrl).cookie(getSessionCookie()).execute();
        if (searchResp.getStatus() == 200) {
            String searchData = searchResp.body();
            if (!StrUtil.isEmpty(searchData)) {
                JSONObject json = JSONUtil.parseObj(searchData);
                if (json.getInt("status") == 0) {
                    int count = json.getJSONObject("data").getInt("count");
                    if (count > 0) {
                        JSONArray result = json.getJSONObject("data").getJSONArray("records");
                        List<DevOpsPipelineHisRespVO> pipelines = new ArrayList<>();
                        result.forEach(jsonObject -> {
                            JSONObject entries = JSONUtil.parseObj(jsonObject);
                            DevOpsPipelineHisRespVO hisRespVO = BeanUtils.toBean(entries, DevOpsPipelineHisRespVO.class);
                            hisRespVO.setStartTime(formatDate(entries.getLong("startTime")));
                            hisRespVO.setEndTime(formatDate(entries.getLong("endTime")));
                            pipelines.add(hisRespVO);
                        });
                        return new PageResult<>(pipelines, Long.valueOf(pipelines.size()));
                    }
                }
            }
        }
        return null;
    }

    /**
     * 将 LocalDateTime 类型的时间转换为 yyyy-MM-dd HH:mm:ss 格式的字符串
     *
     * @param dateTime 待转换的LocalDateTime
     * @return 格式化后的日期字符串，如果 dateTime 为 null 则返回 null
     */
    private String formatLocalDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime.format(formatter);
    }

    @Override
    public DevOpsPipelineBuildLogRespVO getPipelinesBuildLogs(String projectId, String pipelineId, String buildId) {
        String reqUrl = DOMAIN_HOST + "/ms/log/api/user/logs/" + projectId + "/" + pipelineId + "/" + buildId + "?executeCount=1&subTag=&debug=false";
        HttpResponse searchResp = HttpRequest.get(reqUrl).cookie(getSessionCookie()).execute();
        if (searchResp.getStatus() == 200) {
            String searchData = searchResp.body();
            if (!StrUtil.isEmpty(searchData)) {
                JSONObject json = JSONUtil.parseObj(searchData);
                if (json.getInt("status") == 0) {
                    JSONObject result = json.getJSONObject("data");
                    DevOpsPipelineBuildLogRespVO logs = BeanUtils.toBean(result, DevOpsPipelineBuildLogRespVO.class);
                    StringBuffer logStr = new StringBuffer();
                    logs.getLogs().forEach(log -> {
                        JSONObject logObj = JSONUtil.parseObj(log);
                        logStr.append(logObj.getStr("message") + "\n" + " ");
                    });
                    logs.setLogString(logStr.toString());
                    logs.setLogs(null);
                    return logs;
                }
            }
        }
        return null;
    }

    /**
     * 获取流水线详情信息
     *
     * @param projectId
     * @param pipelineId
     * @return
     */
    @Override
    public JSONObject pipelineDetails(String projectId, String pipelineId) {
        String pipelineDetailUrl = DOMAIN_HOST + "/ms/process/api/user/pipelines/" + projectId + "/" + pipelineId;
        HttpResponse searchResp = HttpRequest.get(pipelineDetailUrl).cookie(getSessionCookie()).execute();
        if (searchResp.getStatus() == 200) {
            String searchData = searchResp.body();
            if (!StrUtil.isEmpty(searchData)) {
                JSONObject json = JSONUtil.parseObj(searchData);
                if (json.getInt("status") == 0) {
                    return json.getJSONObject("data");
                }
            }
        }
        return null;
    }

    @Override
    public Boolean deletePipelines(Long programId) {
        return null;
    }
}

