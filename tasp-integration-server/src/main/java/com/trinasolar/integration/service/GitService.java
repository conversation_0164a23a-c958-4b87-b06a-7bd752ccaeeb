package com.trinasolar.integration.service;

import cn.hutool.json.JSONUtil;
import com.trinasolar.integration.constants.GitLabAccessLevel;
import com.trinasolar.integration.controller.devops.vo.GitGroupRespVO;
import com.trinasolar.tasc.framework.common.exception.ErrorCode;
import com.trinasolar.tasc.framework.common.exception.ServerException;
import com.trinasolar.tasc.framework.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.models.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.text.SimpleDateFormat;

import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class GitService {

    @Value("${trinasolar.git.url}")
    protected String gitUrl;
    @Value("${trinasolar.git.token}")
    protected String gitToken;

    public static String BRANCH_NAME = "master";

    public GitLabApi getGitLabApi() {
        return new GitLabApi(gitUrl, gitToken);
    }

    /**
     * 创建git group信息，如存在，则直接返回
     *
     * @param projectEnName
     * @return GitGroup对象
     */
    public GitGroupRespVO createGitGroup(String projectEnName, String parentGroupName) {
        log.info("创建git group信息 项目： {}, 父群组： {}", projectEnName, parentGroupName);
        String groupName = projectEnName;
        Long parentGroupId;
        GitGroupRespVO result;
        if (StringUtils.isEmpty(parentGroupName)) {
            throw new ServerException(1, "创建git group失败，父群组参数为空!");
        }
        // 处理父群组
        Optional<Group> optParentGroup = getGitLabApi().getGroupApi().getOptionalGroup(parentGroupName);
        if (optParentGroup.isEmpty()) {
            // 父群组不存在，尝试创建以判断是权限问题还是真的不存在
            log.info("父群组不存在，尝试创建以判断是权限问题还是真的不存在: {}", parentGroupName);
            GroupParams parentParams = new GroupParams()
                    .withName(parentGroupName)
                    .withVisibility("private")
                    .withPath(parentGroupName);
            try {
                Group parentGroup = getGitLabApi().getGroupApi().createGroup(parentParams);
                parentGroupId = parentGroup.getId();
                log.info("Created parent group: {} with ID: {}", parentGroupName, parentGroupId);
            } catch (GitLabApiException e) {
                if (e.getMessage() != null && e.getMessage().contains("已经被使用")) {
                    // path已被使用，说明父群组存在但无权限查看，直接创建子群组
                    log.warn("Parent group exists but no permission to view, creating subgroup directly: {}", parentGroupName);
                    parentGroupId = null;
                } else {
                    // 其他创建错误，重新查询一次
                    log.warn("Failed to create parent group, retrying query: {}", e.getMessage());
                    optParentGroup = getGitLabApi().getGroupApi().getOptionalGroup(parentGroupName);
                    if (optParentGroup.isPresent()) {
                        parentGroupId = optParentGroup.get().getId();
                        log.info("Found existing parent group: {} with ID: {}", parentGroupName, parentGroupId);
                    } else {
                        throw new RuntimeException("Failed to create or find parent group: " + parentGroupName, e);
                    }
                }
            }
        } else {
            parentGroupId = optParentGroup.get().getId();
            log.info("Found existing parent group: {} with ID: {}", parentGroupName, parentGroupId);
        }

        try {
            // 查询子群组是否存在
            String fullGroupPath = StringUtils.isNotEmpty(parentGroupName) ? parentGroupName + "/" + groupName : groupName;
            Optional<Group> optGroup = getGitLabApi().getGroupApi().getOptionalGroup(fullGroupPath);

            if (optGroup.isPresent()) {
                // 如存在，直接返回
                result = builrGroupResult(optGroup.get());
            } else {
                // 新建git group，注意设置权限为private
                GroupParams params = new GroupParams()
                        .withName(groupName)
                        .withVisibility("private")
                        .withPath(groupName);
                if (parentGroupId != null) {
                    params.withParentId(parentGroupId);
                }
                Group group = getGitLabApi().getGroupApi().createGroup(params);
                result = builrGroupResult(group);
            }
            log.info("gitlab group初始化:gitlab----- {}", JSONUtil.toJsonStr(result));
        } catch (GitLabApiException e) {
            log.error("GitLab API error while creating group: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create GitLab group: " + groupName, e);
        }

        log.info("gitlab group初始化完成:gitlab----- {}", groupName);
        return result;
    }

    private GitGroupRespVO builrGroupResult(Group group) {
        GitGroupRespVO result = new GitGroupRespVO();
        result.setId(group.getId());
        result.setPath(group.getPath());
        result.setName(group.getName());
        result.setWebUrl(group.getWebUrl());
        return result;
    }

    /**
     * 获取项目下的用户提交次数和用户列表，支持时间筛选
     *
     * @param projectId 项目ID
     * @param startTime 开始时间，格式：yyyy-MM-dd HH:mm:ss
     * @param endTime   结束时间，格式：yyyy-MM-dd HH:mm:ss
     * @return 用户提交次数统计，key为用户名，value为提交次数
     */
    public Map<String, Integer> getUserCommitStats(Long projectId, String startTime, String endTime) {
        GitLabApi gitLabApi = getGitLabApi();
        Map<String, Integer> commitStats = new HashMap<>();
        try {
            // 解析时间参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date start = StringUtils.isNotEmpty(startTime) ? sdf.parse(startTime) : null;
            Date end = StringUtils.isNotEmpty(endTime) ? sdf.parse(endTime) : null;
            // 获取提交记录
            List<Commit> commits;
            if (start != null && end != null) {
                commits = gitLabApi.getCommitsApi().getCommits(projectId, null, start, end, null, null, null, null);
            } else {
                commits = gitLabApi.getCommitsApi().getCommits(projectId);
            }
            // 统计每个用户的提交次数
            for (Commit commit : commits) {
                String authorName = commit.getAuthorName();
                commitStats.put(authorName, commitStats.getOrDefault(authorName, 0) + 1);
            }

        } catch (Exception e) {
            log.error("获取用户提交统计失败", e);
            throw new RuntimeException("获取用户提交统计失败", e);
        }

        return commitStats;
    }

    /**
     * 获取群组下所有项目的用户提交次数和用户列表，支持时间筛选
     *
     * @param groupId   群组ID
     * @param startTime 开始时间，格式：yyyy-MM-dd HH:mm:ss
     * @param endTime   结束时间，格式：yyyy-MM-dd HH:mm:ss
     * @return 用户提交次数统计，key为用户名，value为提交次数
     */
    public Map<String, Integer> getUserCommitStatsByGroup(Long groupId, String startTime, String endTime) {
        GitLabApi gitLabApi = getGitLabApi();
        Map<String, Integer> commitStats = new HashMap<>();
        try {
            // 获取群组下的所有项目
            List<Project> projects = gitLabApi.getGroupApi().getProjects(groupId);
            if (CollectionUtils.isEmpty(projects)) {
                log.info("群组下没有找到项目");
                return commitStats;
            }

            // 解析时间参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date start = StringUtils.isNotEmpty(startTime) ? sdf.parse(startTime) : null;
            Date end = StringUtils.isNotEmpty(endTime) ? sdf.parse(endTime) : null;

            // 遍历所有项目，统计提交次数
            for (Project project : projects) {
                List<Commit> commits;
                if (start != null && end != null) {
                    commits = gitLabApi.getCommitsApi().getCommits(project.getId(), null, start, end, null, null, null, null);
                } else {
                    commits = gitLabApi.getCommitsApi().getCommits(project.getId());
                }

                // 统计每个用户的提交次数
                for (Commit commit : commits) {
                    String authorName = commit.getAuthorName();
                    commitStats.put(authorName, commitStats.getOrDefault(authorName, 0) + 1);
                }
            }

        } catch (Exception e) {
            log.error("获取群组用户提交统计失败", e);
            throw new RuntimeException("获取群组用户提交统计失败", e);
        }

        return commitStats;
    }


    public void addUserToGitGroup(String businessDomain, String namespace, List<String> userAccounts, GitLabAccessLevel accessLevel) {
        log.info("add businessDomain is {}, namespace is {}", businessDomain, namespace);
        log.info("add userAccounts is {}", JSONUtil.toJsonStr(userAccounts));
        if (CollectionUtils.isEmpty(userAccounts)) {
            log.info("userAccounts is empty");
            return;
        }
        if (StringUtils.isNotEmpty(businessDomain)) {
            namespace = businessDomain + "/" + namespace;
        }
        GitLabApi gitLabApi = getGitLabApi();
        Optional<Group> optGroup = gitLabApi.getGroupApi().getOptionalGroup(namespace);
        if (optGroup.isEmpty()) {
            log.error("GitLab群组不存在: {}", namespace);
            return;
        }
        List<Member> members = new ArrayList<>();
        Group group = optGroup.get();
        for (String account : userAccounts) {
            try {
                gitLabApi.getUserApi().getOptionalUser(account).ifPresent(e -> {
                    try {
                        Member member = gitLabApi.getGroupApi().addMember(group.getId(), e.getId(), accessLevel.getLevel());
                        members.add(member);
                    } catch (Exception ex) {
                        log.error("添加用户:【{}】异常", account, ex);
                    }
                });
            } catch (Exception ex) {
                log.error("获取用户{}，异常请检查用户在gitlab上是否存在", account, ex);
            }
        }
        log.info("添加gitlab用户到group成功！:{}", JSONUtil.toJsonStr(members));
    }

    /**
     * 从GitLab群组中移除用户
     *
     * @param namespace    群组命名空间
     * @param userAccounts 要移除的用户账号列表
     */
    public void removeUserFromGitGroup(String businessDomain, String namespace, List<String> userAccounts) {
        log.info("remove businessDomain is {}, namespace is {}", businessDomain, namespace);
        log.info("remove userAccounts is {}", JSONUtil.toJsonStr(userAccounts));
        if (StringUtils.isNotEmpty(businessDomain)) {
            namespace = businessDomain + "/" + namespace;
        }
        GitLabApi gitLabApi = getGitLabApi();
        Optional<Group> optGroup = gitLabApi.getGroupApi().getOptionalGroup(namespace);
        if (optGroup.isEmpty()) {
            log.error("GitLab群组不存在: {}", namespace);
            return;
        }
        Group group = optGroup.get();
        for (String account : userAccounts) {
            if (StringUtils.isEmpty(account)) {
                continue;
            }
            try {
                gitLabApi.getUserApi().getOptionalUser(account).ifPresent(e -> {
                    try {
                        gitLabApi.getGroupApi().removeMember(group.getId(), e.getId());
                    } catch (Exception ex) {
                        log.error("移除用户:【{}】异常", account, ex);
                    }
                });
            } catch (Exception ex) {
                log.error("获取用户{}，异常请检查用户在gitlab上是否存在", account, ex);
            }
        }
    }
}
