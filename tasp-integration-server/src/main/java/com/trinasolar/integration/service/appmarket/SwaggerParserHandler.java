package com.trinasolar.integration.service.appmarket;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.integration.dto.apimarket.*;
import com.trinasolar.integration.execption.SwaggerParseException;
import io.swagger.models.Swagger;
import io.swagger.parser.SwaggerParser;
import io.swagger.util.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.http.HttpClient;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * Swagger 2.0 文档解析器（支持 $ref 递归解析、参数和响应结构提取）
 */

@Slf4j
public class SwaggerParserHandler {
    private JsonNode rootNode;
    private Map<String, DefinitionInfo> definitions;
    private RefResolver refResolver;
    private List<String> gateway;

    /**
     * 从类路径加载 Swagger JSON 文件
     */
    public SwaggerParserHandler(String swaggerJsonPath, List<String> gateway) {
        Swagger swagger = new SwaggerParser().parse(swaggerJsonPath);
        rootNode = Json.mapper().valueToTree(swagger);
        definitions = parseDefinitions(rootNode.get("definitions"));
        refResolver = new RefResolver(rootNode);
        this.gateway = gateway;
    }

    public static void main(String[] args) {
        try (InputStream is = SwaggerParserHandler.class.getClassLoader().getResourceAsStream("data.json")) {
            String content = new String(is.readAllBytes(), StandardCharsets.UTF_8);
            SwaggerParserHandler swaggerParserHandler = new SwaggerParserHandler(content, new ArrayList<>());
            List<ApiEndpoint> apiEndpoints = swaggerParserHandler.parseEndpoints();
            String jsonString = JSONObject.toJSONString(apiEndpoints);
            System.out.println(jsonString);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            System.out.println(e.getMessage());
        }
    }

    /**
     * 从 URL 加载 Swagger JSON
     */
    public SwaggerParserHandler(URI swaggerUri, List<String> gateWay) {
        try {
            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder(swaggerUri).build();
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            ObjectMapper mapper = new ObjectMapper();
            rootNode = mapper.readTree(response.body());
        } catch (IOException | InterruptedException e) {
            throw new SwaggerParseException("加载 Swagger 文档失败: " + e.getMessage(), e);
        }
        refResolver = new RefResolver(rootNode);
        definitions = parseDefinitions(rootNode.get("definitions"));
    }

    /**
     * 解析所有接口端点
     *
     * @return 接口端点列表
     */
    public List<ApiEndpoint> parseEndpoints() {
        List<ApiEndpoint> endpoints = new ArrayList<>();
        List<String> host = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(gateway)) {
            host = gateway;
        } else {
            if (rootNode.has("host")) {
                host = List.of(rootNode.get("host").asText());
            }
        }
        JsonNode pathsNode = rootNode.get("paths");
        String basePath = "";
        if (rootNode.get("basePath") == null) {
            basePath = "";
        } else if ("/".equals(rootNode.get("basePath").asText())) {
            basePath = "";
        } else {
            basePath = rootNode.get("basePath").asText();
        }
        if (pathsNode == null) {
            return endpoints;
        }
        String finalBasePath = basePath;
        List<String> fullPath = host.stream().map(e -> e + finalBasePath).collect(Collectors.toList());
        pathsNode.fields().forEachRemaining(pathEntry -> {
            JsonNode pathNode = pathEntry.getValue();
            parseHttpMethods(fullPath, pathEntry.getKey(), pathNode, endpoints);
        });
        return endpoints;
    }

    public Boolean isBaseType(String httpType) {
        List<String> http = List.of("POST", "PUT", "DELETE", "GET");
        return http.contains(httpType);
    }

    private void parseHttpMethods(List<String> fullPath, String path, JsonNode pathNode, List<ApiEndpoint> endpoints) {
        pathNode.fields().forEachRemaining(methodEntry -> {
            String httpMethod = methodEntry.getKey().toUpperCase();
            // 只处理POST PUT DELETE GET请求字段
            if (!isBaseType(httpMethod)) {
                return;
            }
            JsonNode methodNode = methodEntry.getValue();
            ApiEndpoint endpoint = new ApiEndpoint();
            endpoint.setPath(path);
            List<String> fullPaths = fullPath.stream().map(e -> e + path).collect(Collectors.toList());
            endpoint.setFullPath(fullPaths);
            endpoint.setName(getStringValue(methodNode, "summary"));
            if (methodNode.has("consumes")) {
                JsonNode consumesNode = methodNode.get("consumes");
                if (consumesNode.isArray() && !consumesNode.isEmpty()) {
                    endpoint.setConsumes(consumesNode.get(0).asText());
                }
            }
            endpoint.setDesc(getStringValue(methodNode, "description"));
            endpoint.setHttpMethod(httpMethod);
            endpoint.setParameters(parseParameters(methodNode.get("parameters")));
            endpoint.setResponse(parseResponses(methodNode.get("responses")));
            endpoints.add(endpoint);
        });
    }

    /**
     * 解析参数列表（支持 body/header/query/path）
     */
    private List<ParameterInfo> parseParameters(JsonNode paramsNode) {
        if (paramsNode == null) {
            return Collections.emptyList();
        }

        if (!paramsNode.isArray()) {
            return Collections.singletonList(parseParameter(paramsNode));
        }

        Iterator<JsonNode> iterator = paramsNode.elements();
        Spliterator<JsonNode> spliterator = Spliterators.spliteratorUnknownSize(
                iterator,
                Spliterator.ORDERED | Spliterator.SIZED
        );

        return StreamSupport.stream(spliterator, false)
                .map(this::parseParameter)
                .collect(Collectors.toList());
    }

    private ParameterInfo parseParameter(JsonNode paramNode) {
        ParameterInfo param = new ParameterInfo();
        param.setName(getStringValue(paramNode, "name"));
        param.setIn(getStringValue(paramNode, "in", "body"));
        param.setDescription(getStringValue(paramNode, "description"));
        param.setRequired(safeGetBoolean(paramNode, "required", false));
        param.setExampleValue(getExampleValue(paramNode));
        //param.setType(getStringValue(paramNode, "type"));
        // 处理 Body 参数的 schema
        if ("body".equalsIgnoreCase(param.getIn())) {
            JsonNode schemaNode = paramNode.get("schema");
            if (schemaNode != null) {
                handleBodySchema(schemaNode, param);
            }
            if ("object".equalsIgnoreCase(param.getType())) {
                List<ParameterField> fields = param.getFields();
                if (CollectionUtils.isNotEmpty(fields)) {
                    JSONObject jsonObject = new JSONObject();
                    fields.forEach(field -> {
                        buildBodyJson(field, jsonObject);
                    });
                    param.setReqBodyJson(jsonObject.toString());
                }
            } else if ("array".equalsIgnoreCase(param.getType())) {
                ParameterField field = param.getItem();
                JSONObject jsonObject = new JSONObject();
                buildBodyJson(field, jsonObject);
                param.setReqBodyJson(jsonObject.toString());
            }
        } else if ("formData".equalsIgnoreCase(param.getIn()) || "path".equalsIgnoreCase(param.getIn()) || "query".equalsIgnoreCase(param.getIn()) || "header".equalsIgnoreCase(param.getIn())) {
            // 参数名称（请求头名称，如 Authorization）
            String paramName = paramNode.get("name").asText();
            param.setName(paramName);
            // 是否必填（请求头通常可选，除非显式声明）
            boolean required = paramNode.has("required") && paramNode.get("required").asBoolean();
            param.setRequired(required);
            // 参数类型与格式（如 string、integer 等）
            if (paramNode.has("type")) {
                String type = paramNode.get("type").asText();
                param.setType(type);
                if (paramNode.has("format")) {
                    param.setFormat(paramNode.get("format").asText());
                }
            }
            // 枚举值限制（可选）
            if (paramNode.has("enum")) {
                List<String> enumValues = new ArrayList<>();
                paramNode.get("enum").elements().forEachRemaining(node -> enumValues.add(node.asText()));
                param.setEnumValues(enumValues);
            }
        }
        return param;
    }

    private void handleBodySchema(JsonNode schemaNode, ParameterInfo param) {
        checkRef(schemaNode, param);
        parseInlineProperties(param);
    }

    private void checkRef(JsonNode schemaNode, ParameterInfo param) {
        if (schemaNode.has("$ref")) {
            String ref = schemaNode.get("$ref").asText();
            JsonNode refNode = refResolver.resolve(ref);
            // 增强路径解析（支持多级路径）
            String[] refParts = ref.split("/");
            if (refParts.length > 2 && "definitions".equals(refParts[1])) {
                String definitionKey = String.join(".", Arrays.copyOfRange(refParts, 2, refParts.length));
                DefinitionInfo definition = definitions.get(definitionKey);
                if (definition != null && definition.getSchema().has("required")) {
                    param.setSchemaRequired(parseRequiredFields(definition.getSchema().get("required")));
                }
            }
            // 增加空值检查
            if (refNode != null) {
                param.setType(refNode.has("type") ? refNode.get("type").asText() : "");
                param.setSchema(refNode);
            }
        } else {
            // 增加类型检查
            if (schemaNode.isObject()) {
                if (schemaNode.has("required")) {
                    param.setSchemaRequired(parseRequiredFields(schemaNode.get("required")));
                }
                param.setType(schemaNode.has("type") ? schemaNode.get("type").asText() : "");
                param.setSchema(schemaNode);
            }
        }
    }

    // 优化后的required字段解析
    private List<String> parseRequiredFields(JsonNode requiredNode) {
        List<String> required = new ArrayList<>();
        if (requiredNode != null && requiredNode.isArray()) {
            for (JsonNode node : requiredNode) {
                if (node.isTextual()) {
                    required.add(node.asText());
                }
            }
        }
        return required;
    }


    private void parseInlineProperties(ParameterInfo param) {
        buildFields(param.getType(), param.getSchema(), param, param.getSchemaRequired());
    }

    /**
     * @param schema
     * @param schemaRequired
     */
    private void buildFields(String type, JsonNode schema, ParameterInfo parameterInfo, List<String> schemaRequired) {
        // 如果是item或者对象字段，应该继续判断
        if ("object".equalsIgnoreCase(type)) {
            JsonNode properties = schema.get("properties");
            if (properties == null) {
                return;
            }
            List<ParameterField> fields = new ArrayList<>();
            properties.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                ParameterInfo childParameterInfo = new ParameterInfo();
                // 提前转换ref引用
                checkRef(entry.getValue(), childParameterInfo);
                JsonNode fieldSchema = childParameterInfo.getSchema();
                // 对象的每个字段都要转换
                List<String> requireds = new ArrayList<>();
                if (fieldSchema.has("required")) {
                    // 子类
                    requireds = parseRequiredFields(fieldSchema.get("required"));
                }
                String childType = getStringValue(fieldSchema, "type");
                String description = getStringValue(fieldSchema, "description");
                childParameterInfo.setFormat(getStringValue(fieldSchema, "format"));
                childParameterInfo.setName(fieldName);
                childParameterInfo.setType(childType);
                childParameterInfo.setDescription(description);
                buildFields(childType, fieldSchema, childParameterInfo, requireds);
                ParameterField info = buildFieldInfo(childParameterInfo, schemaRequired);
                // 当前字段的请求值类型
                fields.add(info);
            });
            parameterInfo.setFields(fields);
        } else if ("array".equalsIgnoreCase(type)) {
            ParameterInfo childParameterInfo = new ParameterInfo();
            checkRef(schema.get("items"), childParameterInfo);
            JsonNode fieldSchema = childParameterInfo.getSchema();
            String childType = getStringValue(fieldSchema, "type");
            String description = "";
            List<String> childRequireds = new ArrayList<>();
            if ("object".equalsIgnoreCase(childType)) {
                JsonNode childProperties = fieldSchema.get("properties");
                if (childProperties == null) {
                    return;
                }
                if (childProperties.has("required")) {
                    // 子类
                    childRequireds = parseRequiredFields(childProperties.get("required"));
                }
                description = getStringValue(childProperties, "description");
                buildFields(childType, fieldSchema, childParameterInfo, childRequireds);
            } else if ("array".equalsIgnoreCase(childType)) {
                JsonNode childItems = fieldSchema.get("items");
                if (childItems.has("required")) {
                    // 子类
                    childRequireds = parseRequiredFields(childItems.get("required"));
                }
                description = getStringValue(childItems, "description");
                buildFields(childType, fieldSchema, childParameterInfo, childRequireds);
            }
            childParameterInfo.setType(childType);
            childParameterInfo.setDescription(description);
            ParameterField info = buildFieldInfo(childParameterInfo, schemaRequired);
            parameterInfo.setItem(info);
        }
    }


    private boolean isBasicType(String type) {
        return Set.of("string", "number", "integer", "boolean").contains(type.toLowerCase());
    }

    // 递归构建
    private ParameterField buildFieldInfo(ParameterInfo parameterInfo, List<String> schemaRequired) {
        ParameterField field = new ParameterField();
        field.setName(parameterInfo.getName());
        String fieldType = parameterInfo.getType();
        field.setType(fieldType);
        field.setDescription(parameterInfo.getDescription());
        if (CollectionUtils.isNotEmpty(schemaRequired)) {
            field.setRequired(schemaRequired.contains(parameterInfo.getName()));
        }
        field.setFields(parameterInfo.getFields());
        field.setItem(parameterInfo.getItem());
        field.setExample(parameterInfo.getExampleValue());
        field.setFormat(parameterInfo.getFormat());
        return field;
    }


    /**
     * 解析响应结构（处理 200 状态码的响应）
     */
    public List<ResponseInfo> parseResponses(JsonNode responsesNode) {
        List<ResponseInfo> responseList = new ArrayList<>();
        responsesNode.fields().forEachRemaining(entry -> {
            ResponseInfo responseInfo = new ResponseInfo();
            responseInfo.setStatusCode(entry.getKey());
            JsonNode schemaNode = entry.getValue().path("schema");
            if (schemaNode != null) {
                ParameterInfo param = new ParameterInfo();
                handleBodySchema(schemaNode, param);
                responseInfo.setFields(param.getFields());
            }
            // 默认支持对象类型
            List<ParameterField> fields = responseInfo.getFields();
            if (CollectionUtils.isNotEmpty(fields)) {
                JSONObject jsonObject = new JSONObject();
                fields.forEach(field -> {
                    buildBodyJson(field, jsonObject);
                });
                responseInfo.setRespBodyJson(jsonObject.toString());
            }

            responseList.add(responseInfo);
        });
        return responseList;
    }

    private JSONObject buildBodyJson(ParameterField field, JSONObject jsonObject) {
        // 如果是array 下的基础字段
        if (field == null || (!"object".equalsIgnoreCase(field.getType()) && StringUtils.isEmpty(field.getName()))) {
            return null;
        }
        String type = field.getType();
        if ("string".equals(type)) {
            jsonObject.put(field.getName(), StringUtils.isEmpty(field.getExample()) ? "string" : field.getExample());
        } else if ("number".equals(type)) {
            jsonObject.put(field.getName(), StringUtils.isEmpty(field.getExample()) ? 0.0 : field.getExample());
        } else if ("integer".equals(type)) {
            jsonObject.put(field.getName(), StringUtils.isEmpty(field.getExample()) ? 0 : field.getExample());
        } else if ("boolean".equals(type)) {
            jsonObject.put(field.getName(), StringUtils.isEmpty(field.getExample()) ? true : field.getExample());
        } else if ("array".equals(type)) {
            JSONObject bodyJson = buildBodyJson(field.getItem(), new JSONObject());
            List<JSONObject> arrayList = new ArrayList<>();
            if (bodyJson != null) {
                arrayList.add(bodyJson);
            }
            jsonObject.put(field.getName(), arrayList);
        } else if ("object".equals(type)) {
            List<ParameterField> fields = field.getFields();
            JSONObject object = new JSONObject();
            if (CollectionUtils.isNotEmpty(fields)) {
                fields.forEach(e -> {
                    buildBodyJson(e, object);
                });
            }
            if (StringUtils.isEmpty(field.getName())) {
                return object;
            } else {
                jsonObject.put(field.getName(), object);
            }
        } else {
            jsonObject.put(field.getName(), "String");
        }
        return jsonObject;
    }

    /**
     * 安全获取字符串值，支持默认值
     *
     * @param node         JSON 节点
     * @param field        字段名
     * @param defaultValue 默认值
     * @return 字符串值，不存在时返回默认值
     */
    private String safeGetString(JsonNode node, String field, String defaultValue) {
        return Optional.ofNullable(node)
                .flatMap(n -> Optional.ofNullable(n.get(field)))
                .map(JsonNode::asText)
                .orElse(defaultValue);
    }


    /**
     * 解析 definitions 节点
     */
    private Map<String, DefinitionInfo> parseDefinitions(JsonNode definitionsNode) {
        if (definitionsNode == null) return Collections.emptyMap();

        Map<String, DefinitionInfo> map = new LinkedHashMap<>();
        definitionsNode.fields().forEachRemaining(entry -> {
            String name = entry.getKey();
            JsonNode schema = entry.getValue();
            map.put(name, new DefinitionInfo(name, schema));
        });
        return map;
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(JsonNode node, String field) {
        return safeGetString(node, field, "");
    }

    private String getStringValue(JsonNode node, String field, String defaultValue) {
        return Optional.ofNullable(node)
                .flatMap(n -> Optional.ofNullable(n.get(field)))
                .map(JsonNode::asText)
                .orElse(defaultValue);
    }

    private boolean safeGetBoolean(JsonNode node, String field, boolean defaultValue) {
        return Optional.ofNullable(node)
                .flatMap(n -> Optional.ofNullable(n.get(field)))
                .map(JsonNode::asBoolean)
                .orElse(defaultValue);
    }

    private String getExampleValue(JsonNode paramNode) {
        return Optional.ofNullable(paramNode.get("example"))
                .map(JsonNode::asText)
                .orElseGet(() -> Optional.ofNullable(paramNode.get("schema"))
                        .flatMap(s -> Optional.ofNullable(s.get("example")))
                        .map(JsonNode::asText)
                        .orElse(""));
    }


    /**
     * 引用解析器（处理 $ref 路径）
     */
    private static class RefResolver {
        private final JsonNode root;

        public RefResolver(JsonNode root) {
            this.root = root;
        }

        public JsonNode resolve(String ref) {
            if (!ref.startsWith("#/")) {
                throw new SwaggerParseException("无效引用格式: " + ref);
            }

            try {
                // 先对引用路径进行URL解码
                String decodedRef = java.net.URLDecoder.decode(ref, "UTF-8");
                // 然后再拆分路径段
                String[] segments = decodedRef.substring(2).split("/");
                JsonNode current = root;
                for (String segment : segments) {
                    current = current.get(segment);
                    if (current == null) {
                        throw new SwaggerParseException("未找到引用: " + ref);
                    }
                }
                return current;
            } catch (java.io.UnsupportedEncodingException e) {
                throw new SwaggerParseException("引用路径解码失败: " + ref, e);
            }
        }
    }
}
