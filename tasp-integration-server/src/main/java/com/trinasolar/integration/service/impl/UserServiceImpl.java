package com.trinasolar.integration.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.trinasolar.integration.api.UpmsProvider;
import com.trinasolar.integration.api.dto.R;
import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.service.UserService;
import com.trinasolar.integration.util.RequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component("userService")
public class UserServiceImpl implements UserService {

    public static final Integer SUCCESS = 0;

    @Autowired
    private UpmsProvider upmsProvider;

    @Override
    public User getUser() {
        String token = RequestUtils.getToken();
        if (StringUtils.isEmpty(token)) {
            log.error("token is null");
            throw new RuntimeException("token is null");
        }
        R<User> currents;
        try {
            currents = upmsProvider.getCurrents(token);
        } catch (RuntimeException e) {
            log.error("token校验失败" + e.getMessage());
            throw new RuntimeException("未登录");
        }
        if (!SUCCESS.equals(currents.getCode())) {
            throw new RuntimeException("获取当前用户信息失败:" + currents.getData());
        }
        return currents.getData();
    }
}
