package com.trinasolar.integration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.integration.api.entity.*;
import com.trinasolar.integration.constants.RelationTypeConstant;
import com.trinasolar.integration.dao.AppSystemMapper;
import com.trinasolar.integration.dao.ProjectConfigMapper;
import com.trinasolar.integration.execption.AppException;
import com.trinasolar.integration.service.AppProgramComponentService;
import com.trinasolar.integration.service.AppService;
import com.trinasolar.integration.service.AppSystemInitializeService;
import com.trinasolar.integration.service.BaseDevOpsService;
import com.trinasolar.integration.service.SCAService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class AppServiceImpl extends BaseDevOpsService implements AppService {


    public static final String PROJECT_DEVOPS_ENV_INFO = "PROJECT_DEVOPS_ENV_INFO";
    public static final String DEVOPS_BASIC_INFO_KEY = "DEVOPS_BASIC_INFO";
    public static final String PROJECT_BASIC_INFO_KEY = "PROJECT_BASIC_INFO";
    public static final String NAMESPACE = "namespace";
    public static final String PROJECT_CODE = "projectCode";
    public static final String SPRINGCLOUD = "springcloud";

    @Autowired
    private PipelineServiceImpl pipelineService;

    @Autowired
    private ProjectConfigMapper projectConfigMapper;

    @Autowired
    private AppSystemMapper appSystemMapper;

    @Autowired
    @Qualifier("taskExecutor")
    ThreadPoolTaskExecutor executor;

    @Autowired
    private AppProgramComponentService appProgramComponentService;

    @Autowired
    private SCAService scaService;

    @Autowired
    private AppSystemInitializeService appSystemInitializeService;


    @Override
    public void createApp(ApplicationProgram program) {
        //获取环境配置信息
        String programeNameEn = program.getProgramNameEn();
        String gitUrl = program.getGitlabRepoUrl();
        //应用系统ID
        Long applicationId = program.getApplicationId();
        //技术栈（前端、后端）
        String technologyStack = program.getTechnologyStack();
        AppSystem appSystem = appSystemMapper.selectById(applicationId);
        String systemSimpleEnName = appSystem.getEnSimpleName();
        //获取项目配置信息
        List<ProjectConfigDO> projectConfigs = projectConfigMapper.selectByProjectId(applicationId);
        String envJsonStr = projectConfigs.stream().filter(e -> PROJECT_DEVOPS_ENV_INFO.equals(e.getConfigKey())).findFirst().get().getConfigContent();
        String devopsBasicJsonStr = projectConfigs.stream().filter(e -> DEVOPS_BASIC_INFO_KEY.equals(e.getConfigKey())).findFirst().get().getConfigContent();
        String projectBasicJsonStr = projectConfigs.stream().filter(e -> PROJECT_BASIC_INFO_KEY.equals(e.getConfigKey())).findFirst().get().getConfigContent();
        JSONObject devJson = JSONUtil.parseObj(devopsBasicJsonStr);
        JSONObject proJson = JSONUtil.parseObj(projectBasicJsonStr);
        JSONObject envJson = JSONUtil.parseObj(envJsonStr);
        //devops的projectID
        String devOpsCode = devJson.getStr(PROJECT_CODE);
        if (StrUtil.isEmpty(devOpsCode)) {
            log.error("devops的projectID不存在，无法初始化流水线");
            throw new AppException("devops的projectID不存在，无法初始化流水线");
        }
        // 项目空间和git地址进行关联
        String gitHashId = relatedGitRepo(devOpsCode, gitUrl);
        if (StrUtil.isEmpty(gitHashId)) {
            log.error("代码库关联失败，无法初始化流水线");
            throw new AppException("devops的projectID不存在，无法初始化流水线");
        }
        String businessCode = proJson.getStr(NAMESPACE);
        if (StrUtil.isEmpty(businessCode)) {
            log.error("缺失业务域，无法初始化流水线");
            throw new AppException("缺失业务域，无法初始化流水线");
        }
        //初始化流水线
        String[] environments = {"DEV", "TEST", "UAT", "PROD"};

        for (String env : environments) {
            executor.execute(() -> {
                pipelineService.importPipeline(envJson, systemSimpleEnName, programeNameEn, devOpsCode, env, businessCode,
                        gitHashId, technologyStack, getSessionCookie());
            });
        }

        //初始化SCA
        doProgramScaInit(program);
    }

    /**
     * 应用程序创建sca模板
     *
     * @param program
     * @return
     */
    private boolean doProgramScaInit(ApplicationProgram program) {
        Long applicationId = program.getApplicationId();
        AppSystemInitialize one = appSystemInitializeService.getOne(new LambdaQueryWrapper<AppSystemInitialize>()
                .eq(AppSystemInitialize::getAppId, applicationId)
                .eq(AppSystemInitialize::getRelationType, RelationTypeConstant.SCA));
        if (one == null) {
            log.error("未找到应用系统初始化配置，program Name: {}", program.getProgramNameCn());
            throw new AppException("未找到应用系统初始化配置");
        }
        Integer modelId = scaService.createModel(program, Integer.valueOf(one.getRelationId()));
        AppProgramComponent appProgramComponent = new AppProgramComponent();
        appProgramComponent.setProgramId(program.getId());
        appProgramComponent.setComponent(RelationTypeConstant.SCA);
        appProgramComponent.setComponentId(String.valueOf(modelId));
        appProgramComponent.setProjectId(one.getRelationId());
        appProgramComponent.setSystemId(applicationId);
        return appProgramComponentService.save(appProgramComponent);
    }

    @Override
    public boolean doProgramScaInitBatch(List<ApplicationProgram> programs) {
        for (ApplicationProgram program : programs) {
            createApp(program);
        }
        return true;
    }

    private String getAliasName(String gitUrl) {
        // 正则匹配所有协议格式（http/https/ssh）和端口号，提取仓库路径
        // 调整后的正则处理（行号~828附近）
        String regex = "^(?:https?|ssh)://(?:[^/]+@)?[^/:]+(?::\\d+)?/(.+?)(?:\\.git)?$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(gitUrl);
        if (matcher.find()) {
            // 直接返回完整路径，不进行二次分割
            return matcher.group(1);
        }
        return "";
    }

    private String relatedGitRepo(String projectCode, String gitUrl) {
        if (StrUtil.isEmpty(gitUrl)) {
            log.error("代码库地址不存在，无法初始化流水线");
            return "";
        }

        String relatedGitUrl = DOMAIN_HOST + "/ms/repository/api/user/repositories/[projectId]/";
        String aliasName = getAliasName(gitUrl);
        String searchGitUrl = DOMAIN_HOST + "/ms/repository/api/user/repositories/" + projectCode + "/search?aliasName=" + aliasName + "&page=1&pageSize=20&sortBy=&sortType=";
        try (HttpResponse searchResp = HttpRequest.get(searchGitUrl).cookie(getSessionCookie()).execute()) {
            if (searchResp.getStatus() == 200) {
                String searchData = searchResp.body();
                if (!StrUtil.isEmpty(searchData)) {
                    JSONObject json = JSONUtil.parseObj(searchData);
                    if (json.getInt("status") == 0) {
                        int countExistGit = json.getJSONObject("data").getInt("count");
                        if (countExistGit == 1) {
                            //已存在，返回已存在的HashID
                            JSONObject result = json.getJSONObject("data").getJSONArray("records").getJSONObject(0);
                            return result.getStr("repositoryHashId");
                        }

                    }
                }
            }
        }
        String formData = "{\n" +
                "    \"@type\": \"gitlab\",\n" +
                "    \"aliasName\": \"" + aliasName + "\",\n" +
                "    \"credentialId\": \"new_gitlab.global\",\n" +
                "    \"projectName\": \"" + aliasName + "\",\n" +
                "    \"url\": \"" + gitUrl + "\",\n" +
                "    \"authType\": \"HTTP\",\n" +
                "    \"svnType\": \"ssh\",\n" +
                "    \"tenant\": \"\",\n" +
                "    \"userName\": \"121100\"\n" +
                "}";
        HttpResponse response = HttpRequest
                .post(relatedGitUrl.replace("[projectId]", projectCode))
                .cookie(getSessionCookie())
                .contentType("application/json")
                .body(formData).execute();
        if (response.getStatus() == 200) {
            String result = response.body();
            if (!StrUtil.isEmpty(result)) {
                JSONObject json = JSONUtil.parseObj(result);
                if (json.getInt("status") == 0) {
                    //返回新建的hashId
                    return json.getJSONObject("data").getStr("hashId");
                }
            }
        }
        return "";
    }


}

