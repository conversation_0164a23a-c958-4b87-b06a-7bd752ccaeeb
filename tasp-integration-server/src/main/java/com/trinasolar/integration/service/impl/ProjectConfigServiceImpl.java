package com.trinasolar.integration.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.trinasolar.integration.api.entity.ProjectConfigDO;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigPageReqVO;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigSaveReqVO;
import com.trinasolar.integration.dao.ProjectConfigMapper;
import com.trinasolar.integration.service.ProjectConfigService;
import com.trinasolar.tasc.framework.common.pojo.PageResult;
import com.trinasolar.tasc.framework.common.util.object.BeanUtils;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 项目配置信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectConfigServiceImpl implements ProjectConfigService {


    @Resource
    private ProjectConfigMapper projectConfigMapper;

    /**
     *  查询应用系统devops初始是否完成
     * @param appSystemId
     * @return
     */
    @Override
    public Boolean initCheck(Long appSystemId) {
        List<ProjectConfigDO> projectConfigDOS = projectConfigMapper.selectByProjectId(appSystemId);
        return CollectionUtil.isNotEmpty(projectConfigDOS)&&projectConfigDOS.size()==3;
    }
}