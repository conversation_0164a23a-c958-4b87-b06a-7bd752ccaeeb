package com.trinasolar.integration.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.integration.api.entity.ComponentLogoDO;
import com.trinasolar.integration.controller.component.ComponentLogoBO;
import com.trinasolar.integration.dao.ComponentLogoMapper;
import com.trinasolar.integration.service.ComponentLogoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ComponentLogoServiceImpl extends ServiceImpl<ComponentLogoMapper, ComponentLogoDO> implements ComponentLogoService {

    @Autowired
    private ComponentLogoMapper componentLogoMapper;

    @Override
    public List<ComponentLogoBO> componentLogo() {
        return componentLogoMapper.componentLogoList();
    }
}

