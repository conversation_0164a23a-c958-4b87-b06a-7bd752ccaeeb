package com.trinasolar.integration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.api.entity.ProductServicePO;
import com.trinasolar.integration.api.entity.UserProductFavoritePO;
import com.trinasolar.integration.api.service.ProductService;
import com.trinasolar.integration.api.service.UserProductFavoriteService;
import com.trinasolar.integration.convert.ProductConvert;
import com.trinasolar.integration.dto.product.ProductDTO;
import com.trinasolar.integration.dto.product.ProductRespDTO;
import com.trinasolar.integration.service.ProductServerService;
import com.trinasolar.integration.util.RequestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ProductServerServiceImpl implements ProductServerService {

    @Autowired
    private UserProductFavoriteService userProductFavoriteService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductConvert productConvert;

    @Override
    public ProductRespDTO list() {
        User currentUser = RequestUtils.getCurrentUser();
        String id = currentUser.getId();
        List<UserProductFavoritePO> userProductFavorites = userProductFavoriteService.getFavoriteProductId(Long.parseLong(id));
        List<Long> favoritesIds = userProductFavorites.stream().map(UserProductFavoritePO::getProductId).collect(Collectors.toList());
        List<ProductServicePO> all = productService.getAllProducts();
        List<ProductDTO> allProducts = productConvert.toProductDTOs(all);
        List<ProductDTO> favoriteProducts = allProducts.stream().filter(e -> favoritesIds.contains(e.getId())).collect(Collectors.toList());
        ProductRespDTO respDTO = new ProductRespDTO();
        respDTO.setFavoriteProducts(favoriteProducts).setAllProducts(allProducts);
        return respDTO;
    }

    @Override
    public Long getComponentCount() {
        LambdaQueryWrapper<ProductServicePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.notLike(ProductServicePO::getName, "建设中").eq(ProductServicePO::getType, "共享组件");
        return productService.count(queryWrapper);
    }
}
