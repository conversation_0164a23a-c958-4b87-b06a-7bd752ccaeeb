package com.trinasolar.integration.service;

import com.trinasolar.integration.api.vo.UpdateMemberDTO;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectAdminDTO;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectResp;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectRespVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectSaveReqVO;

import java.util.List;
import java.util.Map;

public interface DevOpsProjectService {

    DevOpsProjectRespVO createProject(DevOpsProjectSaveReqVO devOpsProjectSaveReqVO);

    DevOpsProjectResp<Boolean> updateProject(Long id, List<String> delCodes, List<String> addCodes);

    DevOpsProjectResp<List<DevOpsProjectAdminDTO>> getAdminList(String projectCode);


    void addCicdPlugins(String projectId);

    Map<String, String> addEnvGroup(String projectId);

    void enableDockerImgRepoPublic(String projectId);

    void relatedAllCredentials(String projectId);

    void updateMember4Project(UpdateMemberDTO updateMemberDTO);

    DevOpsProjectRespVO getExistProject(String projectName);

}
