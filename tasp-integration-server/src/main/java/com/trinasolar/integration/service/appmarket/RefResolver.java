package com.trinasolar.integration.service.appmarket;


import com.fasterxml.jackson.databind.JsonNode;
import com.trinasolar.integration.execption.SwaggerParseException;

/**
 * JSON 引用解析器（处理 $ref）
 */
public class RefResolver {
    private final JsonNode rootNode;

    public RefResolver(JsonNode rootNode) {
        this.rootNode = rootNode;
    }

    /**
     * 解析 $ref 引用
     * @param ref JSON 引用字符串（如 #/definitions/Name）
     * @return 解析后的 JsonNode
     */
    public JsonNode resolve(String ref) {
        if (!ref.startsWith("#/")) {
            throw new SwaggerParseException("无效的引用格式: " + ref);
        }

        String[] segments = ref.substring(2).split("/");
        JsonNode currentNode = rootNode;

        for (String segment : segments) {
            currentNode = currentNode.get(segment);
            if (currentNode == null) {
                throw new SwaggerParseException("未找到引用: " + ref);
            }
        }

        return currentNode;
    }
}
