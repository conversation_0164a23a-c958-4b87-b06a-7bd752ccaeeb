package com.trinasolar.integration.service;

import cn.hutool.json.JSONObject;
import com.trinasolar.integration.api.vo.PipelineRespVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsPipelineBuildLogRespVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsPipelineHisRespVO;
import com.trinasolar.tasc.framework.common.pojo.PageResult;

import java.util.List;

public interface PipelineService {
    PageResult<PipelineRespVO> getPipelines(Long programId);

    JSONObject deploy(Long projectId, String pipelineId);

    Boolean grant(Long projectId, String appName, List<String> userCodes);

    Boolean revoke(Long projectId, String appName, List<String> userCodes);

    PageResult<DevOpsPipelineHisRespVO> getPipelinesHistory(String projectId, String pipelineId);

    DevOpsPipelineBuildLogRespVO getPipelinesBuildLogs(String projectId, String pipelineId, String buildId);

    JSONObject pipelineDetails(String projectId, String pipelineId);

    Boolean deletePipelines(Long programId);
}
