package com.trinasolar.integration.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.integration.api.entity.OpenSourceComptBaselineRegister;
import com.trinasolar.integration.dao.OpenSourceComptBaselineRegisterMapper;
import com.trinasolar.integration.service.OpenSourceComptBaselineRegisterService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 开源组件基线登记 Service 实现类
 *
 * <AUTHOR>
@Service
public class OpenSourceComptBaselineRegisterServiceImpl extends ServiceImpl<OpenSourceComptBaselineRegisterMapper, OpenSourceComptBaselineRegister> implements OpenSourceComptBaselineRegisterService {
    
    @Override
    public boolean createOpenSourceComptBaselineRegister(OpenSourceComptBaselineRegister register) {
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        register.setCreateTime(now);
        register.setUpdateTime(now);
        register.setDelFlag(0);
        return save(register);
    }
    
    @Override
    public boolean updateOpenSourceComptBaselineRegister(OpenSourceComptBaselineRegister register) {
        // 设置更新时间
        register.setUpdateTime(LocalDateTime.now());
        return updateById(register);
    }
    
    @Override
    public boolean deleteOpenSourceComptBaselineRegister(Long id) {
        // 使用逻辑删除
        OpenSourceComptBaselineRegister register = new OpenSourceComptBaselineRegister();
        register.setId(id);
        register.setDelFlag(1); // 设置已删除
        register.setUpdateTime(LocalDateTime.now());
        return updateById(register);
    }
    
    @Override
    public OpenSourceComptBaselineRegister getOpenSourceComptBaselineRegister(Long id) {
        return getById(id);
    }
    
    @Override
    public List<OpenSourceComptBaselineRegister> getOpenSourceComptBaselineRegisterList() {
        // 查询未删除的记录
        return lambdaQuery().eq(OpenSourceComptBaselineRegister::getDelFlag, 0).list();
    }
    
    @Override
    public OpenSourceComptBaselineRegister getOpenSourceComptBaselineRegisterByName(String name) {
        return lambdaQuery().eq(OpenSourceComptBaselineRegister::getName, name)
                .eq(OpenSourceComptBaselineRegister::getDelFlag, 0)
                .one();
    }
}