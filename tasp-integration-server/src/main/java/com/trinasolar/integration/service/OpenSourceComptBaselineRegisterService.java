package com.trinasolar.integration.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.integration.api.entity.OpenSourceComptBaselineRegister;
import com.trinasolar.tasc.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 开源组件基线登记 Service 接口
 *
 * <AUTHOR>
public interface OpenSourceComptBaselineRegisterService extends IService<OpenSourceComptBaselineRegister> {
    
    /**
     * 创建开源组件基线登记
     *
     * @param register 开源组件基线登记
     * @return 是否创建成功
     */
    boolean createOpenSourceComptBaselineRegister(OpenSourceComptBaselineRegister register);
    
    /**
     * 更新开源组件基线登记
     *
     * @param register 开源组件基线登记
     * @return 是否更新成功
     */
    boolean updateOpenSourceComptBaselineRegister(OpenSourceComptBaselineRegister register);
    
    /**
     * 删除开源组件基线登记
     *
     * @param id 主键
     * @return 是否删除成功
     */
    boolean deleteOpenSourceComptBaselineRegister(Long id);
    
    /**
     * 获取开源组件基线登记
     *
     * @param id 主键
     * @return 开源组件基线登记
     */
    OpenSourceComptBaselineRegister getOpenSourceComptBaselineRegister(Long id);
    
    /**
     * 获取开源组件基线登记列表
     *
     * @return 开源组件基线登记列表
     */
    List<OpenSourceComptBaselineRegister> getOpenSourceComptBaselineRegisterList();
    
    /**
     * 根据组件名称获取基线登记
     *
     * @param name 组件名称
     * @return 开源组件基线登记
     */
    OpenSourceComptBaselineRegister getOpenSourceComptBaselineRegisterByName(String name);
}