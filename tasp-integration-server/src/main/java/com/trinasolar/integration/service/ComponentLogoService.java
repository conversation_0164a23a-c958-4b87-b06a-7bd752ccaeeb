package com.trinasolar.integration.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.integration.api.entity.ComponentLogoDO;
import com.trinasolar.integration.api.entity.ShareComponentDO;
import com.trinasolar.integration.controller.component.ComponentLogoBO;
import com.trinasolar.integration.controller.component.ShareComponentBO;
import com.trinasolar.integration.controller.component.ShareComponentReq;
import com.trinasolar.integration.controller.component.ShareComponentVO;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface ComponentLogoService extends IService<ComponentLogoDO> {
    List<ComponentLogoBO> componentLogo();
}
