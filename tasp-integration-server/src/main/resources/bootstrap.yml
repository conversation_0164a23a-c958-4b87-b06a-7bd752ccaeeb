spring:
  application:
    name: kepler-integration
#    version: 1.0.0
#    profiles:
#      active: ${ACTIVE_ENV:default}
#  config:
#    import: optional:nacos:${spring.application.name}.yaml
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:***********}:${NACOS_PORT:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:LU<PERSON>cheng@384}
        namespace: ${NACOS_NS:dev}
        register-enabled: true
        heart-beat-timeout: 15000
        heart-beat-interval: 5000
        ip-delete-timeout: 30000
      config:
        contextPath: /nacos
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        file-extension: yaml
        shared-configs:
          - application.yml
        namespace: ${NACOS_NS:dev}
