{"model": {"name": "[UAT][APP_NAME]", "desc": "", "stages": [{"containers": [{"@type": "trigger", "id": "0", "name": "构建触发", "elements": [{"@type": "manualTrigger", "name": "手动触发", "id": "T-1-1-1", "canElementSkip": true, "useLatestParameters": false, "executeCount": 1, "canRetry": false, "version": "1.*", "classType": "manualTrigger", "elementEnable": true, "atomCode": "manualTrigger", "taskAtom": ""}], "params": [{"id": "TEAM_NAME", "required": true, "type": "STRING", "defaultValue": "[TEAM_NAME]", "desc": "团队名称，英文，在容器部署的namespce会引用到", "readOnly": false}, {"id": "PROJ_NAME", "required": true, "type": "STRING", "defaultValue": "[PROJECT_NAME]", "desc": "项目别名", "readOnly": false}, {"id": "APP_NAME", "required": true, "type": "STRING", "defaultValue": "[APP_NAME]", "desc": "应用微服务名称", "readOnly": false}, {"id": "BRANCH", "required": true, "type": "REPOSITORY_BRANCH", "defaultValue": "{\"repositoryHashId\":\"[GIT_HASH_ID]\",\"branchName\":\"[BRANCH_NAME]\"}", "options": [], "desc": "代码库分支名称，配置好后会自动传入拉取代码库插件里", "relativePath": "[GIT_HASH_ID]", "scmType": "CODE_GITLAB", "searchUrl": "/repository/api/user/gitlab/[PROJECT_ID]/[GIT_HASH_ID]/branches?search=searchName", "readOnly": false}, {"id": "VERSION", "required": true, "type": "STRING", "defaultValue": "${{BK_CI_MAJOR_VERSION}}.${{BK_CI_MINOR_VERSION}}.${{BK_CI_FIX_VERSION}}.${{BK_CI_BUILD_NO}}", "desc": "版本号", "readOnly": false}, {"id": "WORK_DIR", "required": true, "type": "STRING", "defaultValue": "[WORK_DIR]", "desc": "工作目录，源码所在目录 ，/开头例如/adx-cdf-n", "readOnly": false}, {"id": "ENV", "required": true, "type": "ENUM", "defaultValue": "uat", "options": [{"key": "dev", "value": "dev"}, {"key": "test", "value": "test"}, {"key": "uat", "value": "uat"}, {"key": "prod", "value": "prod"}], "desc": "部署环境", "readOnly": false}, {"id": "MEMORY", "required": true, "type": "STRING", "defaultValue": "1000", "desc": "容器内存，默认1000Mi，单位Mi", "readOnly": false}, {"id": "JAR_MEM", "required": true, "type": "STRING", "defaultValue": "2g", "desc": "微服务启动内存，默认2g", "readOnly": false}, {"id": "REPLICAS", "required": true, "type": "STRING", "defaultValue": "1", "desc": "副本数量", "readOnly": false}, {"id": "halp", "required": true, "type": "STRING", "defaultValue": "", "desc": "多副本部署的HA地址", "readOnly": false}, {"id": "CPACK_ADDR", "required": true, "type": "STRING", "defaultValue": "bizdevops-crepo.trinasolar.com/${{BK_CI_PROJECT_NAME}}/docker-local", "desc": "归档至镜像制品库的地址", "readOnly": false}, {"id": "ENV_PROFILE", "required": true, "type": "TEXTAREA", "defaultValue": "\"PLATFORM\"=\"rancher\"", "desc": "服务部署的环境变量会写入到yaml文件中；填写示例：\"KEY\"=\"VALUE\" 多个变量请分行填写", "readOnly": false}, {"id": "YAML_NAME", "required": true, "type": "STRING", "defaultValue": "deployment.yaml", "desc": "yaml文件名称", "readOnly": false}, {"id": "ROLLOUT_VERSION", "required": true, "type": "STRING", "defaultValue": "", "desc": "服务回滚使用，默认不填", "readOnly": false}, {"id": "BK_CI_MAJOR_VERSION", "required": false, "type": "STRING", "defaultValue": "1", "desc": "主版本", "readOnly": false}, {"id": "BK_CI_MINOR_VERSION", "required": false, "type": "STRING", "defaultValue": "0", "desc": "特性版本", "readOnly": false}, {"id": "BK_CI_FIX_VERSION", "required": false, "type": "STRING", "defaultValue": "0", "desc": "修正版本", "readOnly": false}], "buildNo": {"buildNo": 1, "buildNoType": "SUCCESS_BUILD_INCREMENT", "required": true}, "containerId": "0", "containerHashId": "c-e397bfb9edf34166a2ad170800844d61", "matrixGroupFlag": false, "classType": "trigger"}], "id": "stage-1", "name": "开始", "tag": ["28ee946a59f64949a74f3dee40a1bda4"], "fastKill": false, "finally": false}, {"containers": [{"@type": "vmBuild", "id": "1", "name": "编译打包", "elements": [{"@type": "marketBuild", "name": "修改apollo配置(需要时启用)", "id": "", "atomCode": "ApolloDashboard", "version": "1.*", "data": {"input": {"env": "${{ENV}}", "teamName": "${{TEAM_NAME}}", "projectName": "${{PROJ_NAME}}", "appName": "${{APP_NAME}}"}, "output": {}, "namespace": "", "config": {"canPauseBeforeRun": true}}, "additionalOptions": {"enable": false, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": true, "subscriptionPauseUser": "liud", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": false, "taskAtom": ""}, {"@type": "linuxScript", "name": "设置全局变量", "id": "", "scriptType": "SHELL", "script": "# 您可以通过setEnv函数设置插件间传递的参数\n# setEnv \"FILENAME\" \"package.zip\"\n# 然后在后续的插件的表单中使用${FILENAME}引用这个变量\n\n# 您可以在质量红线中创建自定义指标，然后通过setGateValue函数设置指标值\n# setGateValue \"CodeCoverage\" $myValue\n# 然后在质量红线选择相应指标和阈值。若不满足，流水线在执行时将会被卡住\n\n# cd ${WORKSPACE} 可进入当前工作空间目录\n\nsetEnv \"VERSION\" \"${{VERSION}}\"\necho ${{VERSION}}\n# setEnv \"BRANCH\" \"${{BRANCH}}\"\n# echo ${BRANCH}\nsetEnv \"WORK_DIR\" \"${{WORK_DIR}}\"\necho ${{WORK_DIR}}\nsetEnv \"ENV\" \"${{ENV}}\"\necho ${ENV}\nsetEnv \"MEMORY\" \"${{MEMORY}}\"\necho ${MEMORY}\nsetEnv \"REPLICAS\" \"${{REPLICAS}}\"\necho ${REPLICAS}\nsetEnv \"CPACK_ADDR\" \"${{CPACK_ADDR}}\"\necho ${CPACK_ADDR}\nsetEnv \"ENV_PROFILE\" \"${{ENV_PROFILE}}\"\n#echo ${ENV_PROFILE}\nsetEnv \"YAML_NAME\" \"${{YAML_NAME}}\"\necho ${YAML_NAME}\nsetEnv \"BK_CI_PIPELINE_NAME\" \"${{BK_CI_PIPELINE_NAME}}\"\necho ${BK_CI_PIPELINE_NAME}\nsetEnv \"BK_CI_PROJECT_NAME\" \"${{BK_CI_PROJECT_NAME}}\"\necho ${BK_CI_PROJECT_NAME}\nsetEnv \"TEAM_NAME\" \"${{TEAM_NAME}}\"\necho ${TEAM_NAME}\nsetEnv \"PROJ_NAME\" \"${{PROJ_NAME}}\"\necho ${PROJ_NAME}\nsetEnv \"APP_NAME\" \"${{APP_NAME}}\"\necho ${APP_NAME}\nsetEnv \"JAR_MEM\" \"${{JAR_MEM}}\"\necho ${JAR_MEM}", "continueNoneZero": false, "enableArchiveFile": false, "archiveFile": "", "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "liud", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "version": "1.*", "classType": "linuxScript", "elementEnable": true, "atomCode": "linuxScript", "taskAtom": ""}, {"@type": "CODE_GITLAB", "name": "拉取Gitlab仓库代码", "id": "", "repositoryHashId": "[GIT_HASH_ID]", "strategy": "FRESH_CHECKOUT", "path": "", "enableSubmodule": true, "gitPullMode": {"type": "BRANCH", "value": "[BRANCH_NAME]"}, "repositoryType": "ID", "repositoryName": "", "enableVirtualMergeBranch": false, "ignoreSsl": false, "branchVariable": "${{BRANCH}}", "executeCount": 1, "version": "1.*", "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "classType": "CODE_GITLAB", "elementEnable": true, "atomCode": "CODE_GITLAB", "taskAtom": ""}, {"@type": "marketBuild", "name": "代码扫描-CodeCC", "id": "", "atomCode": "CodeCCCheckAtom", "version": "1.*", "data": {"input": {"beAutoLang": false, "languages": ["JAVA"], "checkerSetType": "normal", "tools": ["CCN"], "asyncTask": false, "asyncTaskId": "", "goPath": "", "pyVersion": "py3", "scriptType": "SHELL", "script": "# Coverity/Klocwork将通过调用编译脚本来编译您的代码，以追踪深层次的缺陷\n# 请使用依赖的构建工具如maven/cmake等写一个编译脚本build.sh\n# 确保build.sh能够编译代码\n# cd path/to/build.sh\n# sh build.sh", "languageRuleSetMap": {"JAVA_RULE": ["codecc_default_ccn_java"]}, "C_CPP_RULE": [], "checkerSetEnvType": "prod", "multiPipelineMark": "", "rtxReceiverType": "1", "rtxReceiverList": [], "botWebhookUrl": "", "botRemindRange": "2", "botRemindSeverity": "7", "botRemaindTools": [], "emailReceiverType": "1", "emailReceiverList": [], "emailCCReceiverList": [], "instantReportStatus": "2", "reportDate": [], "reportTime": "", "reportTools": [], "toolScanType": "1", "incrementScanType": "lastScan", "baselineCommit": "", "diffBranch": "", "byFile": false, "prohibitIgnore": false, "transferAuthorList": [], "path": [], "customPath": [], "scanTestSource": false, "openScanPrj": false, "openScanFilterEnable": false, "JAVA_RULE": ["codecc_default_ccn_java"], "mrCommentEnable": true}, "output": {"BK_CI_CODECC_TASK_ID": "string", "BK_CI_CODECC_TASK_STATUS": "string"}, "namespace": ""}, "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": true, "taskAtom": ""}, {"@type": "marketBuild", "name": "SCA组件扫描", "id": "", "atomCode": "FlowInSCATrinasolar", "version": "1.*", "data": {"input": {"autoLang": "1", "language": "", "thirdPartyPath": "", "emailEnabled": "0", "emails": "", "webhookEnabled": "0", "webhookUrl": "", "pushCondition": "0", "pushReport": true, "isUrgent": false}, "output": {}, "namespace": ""}, "additionalOptions": {"enable": false, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "liud", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": false, "taskAtom": ""}, {"@type": "marketBuild", "name": "拉取编译脚本", "id": "", "atomCode": "GetCustomizeArchiveBkRepoCroEnv", "version": "1.*", "data": {"input": {"srcPaths": "https://bizdevops-repo.trinasolar.com/generic/g6ac9f/generic-public/template/backend/backend_compile.sh", "destPath": "", "credentialId": "bkrepo.global"}, "output": {}, "namespace": ""}, "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "liud", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": true, "taskAtom": ""}, {"@type": "linuxScript", "name": "执行编译脚本", "id": "", "scriptType": "SHELL", "script": "# 您可以通过setEnv函数设置插件间传递的参数\n# setEnv \"FILENAME\" \"package.zip\"\n# 然后在后续的插件的表单中使用${FILENAME}引用这个变量\n\n# 您可以在质量红线中创建自定义指标，然后通过setGateValue函数设置指标值\n# setGateValue \"CodeCoverage\" $myValue\n# 然后在质量红线选择相应指标和阈值。若不满足，流水线在执行时将会被卡住\n\n# cd ${WORKSPACE} 可进入当前工作空间目录\necho \">>>>>打印脚本内容\"\ncat backend_compile.sh\necho \"\"\necho \">>>>>结束\"\nbash backend_compile.sh", "continueNoneZero": false, "enableArchiveFile": false, "archiveFile": "", "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "version": "1.*", "classType": "linuxScript", "elementEnable": true, "atomCode": "linuxScript", "taskAtom": ""}, {"@type": "marketBuild", "name": "拉取Dockerfile模板", "id": "", "atomCode": "GetCustomizeArchiveBkRepoCroEnv", "version": "1.*", "data": {"input": {"srcPaths": "https://bizdevops-repo.trinasolar.com/generic/g6ac9f/generic-public/template/backend/jdk17/Dockerfile", "destPath": "", "credentialId": "bkrepo.global"}, "output": {}, "namespace": ""}, "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": true, "taskAtom": ""}, {"@type": "marketBuild", "name": "拉取启动脚本", "id": "", "atomCode": "GetCustomizeArchiveBkRepoCroEnv", "version": "1.*", "stepId": "P9Z", "data": {"input": {"srcPaths": "https://bizdevops-repo.trinasolar.com/generic/g6ac9f/generic-public/template/backend/start.sh", "destPath": "", "credentialId": "bkrepo.global"}, "output": {}, "namespace": ""}, "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": true, "taskAtom": ""}, {"@type": "marketBuild", "name": "拉取修改dockerfile脚本", "id": "", "atomCode": "GetCustomizeArchiveBkRepoCroEnv", "version": "1.*", "data": {"input": {"srcPaths": "https://bizdevops-repo.trinasolar.com/generic/g6ac9f/generic-public/template/backend/backend_dockerfile_change.sh", "destPath": "", "credentialId": "bkrepo.global"}, "output": {}, "namespace": ""}, "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "liud", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": true, "taskAtom": ""}, {"@type": "linuxScript", "name": "执行修改Dockerfile脚本", "id": "", "scriptType": "SHELL", "script": "# 您可以通过setEnv函数设置插件间传递的参数\n# setEnv \"FILENAME\" \"package.zip\"\n# 然后在后续的插件的表单中使用${FILENAME}引用这个变量\n\n# 您可以在质量红线中创建自定义指标，然后通过setGateValue函数设置指标值\n# setGateValue \"CodeCoverage\" $myValue\n# 然后在质量红线选择相应指标和阈值。若不满足，流水线在执行时将会被卡住\n\n# cd ${WORKSPACE} 可进入当前工作空间目录\necho \">>>>>打印脚本内容\"\ncat backend_dockerfile_change.sh\necho \"\"\necho \">>>>>结束\"\nbash backend_dockerfile_change.sh", "continueNoneZero": false, "enableArchiveFile": false, "archiveFile": "", "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "version": "1.*", "classType": "linuxScript", "elementEnable": true, "atomCode": "linuxScript", "taskAtom": ""}, {"@type": "singleArchive", "name": "归档jar包", "id": "", "filePath": ".${{WORK_DIR}}/target/app.jar", "destPath": "./${{APP_NAME}}/${{ENV}}/${{VERSION}}", "customize": true, "executeCount": 1, "version": "1.*", "additionalOptions": {"enable": false, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "stepId": "BXp", "classType": "singleArchive", "elementEnable": false, "atomCode": "singleArchive", "taskAtom": ""}, {"@type": "marketBuild", "name": "SCA-制品扫描（需要时启用）", "id": "", "atomCode": "FlowInlSCA", "version": "1.*", "data": {"input": {"fullPath": "https://bizdevops-repo.trinasolar.com/generic/${{BK_CI_PROJECT_NAME}}/custom/${{APP_NAME}}/${{ENV}}/${{VERSION}}/app.jar", "emailEnabled": "0", "emails": "", "webhookUrl": "", "pushCondition": "0", "pushReport": true, "isUrgent": false}, "output": {}, "namespace": ""}, "additionalOptions": {"enable": false, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "302192", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": false, "taskAtom": ""}, {"@type": "marketBuild", "name": "镜像上传", "id": "", "atomCode": "DockerBuildAndPushImage", "version": "1.*", "data": {"input": {"sourceMirrorTicketPair": "${{CPACK_ADDR}} bkrepo.global", "targetImage": "${{CPACK_ADDR}}/${{APP_NAME}}-${{ENV}}", "targetTicketId": "bkrepo.global", "targetImageTag": "${{VERSION}}", "dockerBuildDir": ".${{WORK_DIR}}", "dockerFilePath": ".${{WORK_DIR}}/Dockerfile", "dockerBuildArgs": ""}, "output": {"BK_DOCKER_TARGE_IMAGE_NAME": "string", "BK_DOCKER_TARGE_IMAGE_TAG": "string"}, "namespace": ""}, "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": true, "taskAtom": ""}], "baseOS": "LINUX", "vmNames": [], "maxQueueMinutes": 60, "maxRunningMinutes": 900, "buildEnv": {"maven": "3.9.9", "java": "17"}, "agentBind": true, "dispatchType": {"buildType": "DOCKER", "value": "bkdevops/centos7.2:v1", "imageType": "BKDEVOPS", "credentialId": "", "credentialProject": "", "imageCode": "", "imageVersion": "", "imageName": "", "performanceConfigId": 0, "imageRepositoryUserName": "", "imageRepositoryPassword": "", "imagePublicFlag": false, "imageRDType": "", "recommendFlag": true}, "showBuildResource": false, "enableExternal": false, "containerId": "1", "containerHashId": "c-20da4017da634f47860b04fb9b1e1aa3", "jobControlOption": {"enable": true, "prepareTimeout": 10, "timeout": 900, "timeoutVar": "900", "runCondition": "STAGE_RUNNING", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "dependOnType": "ID", "dependOnId": [], "dependOnName": "", "continueWhenFailed": false}, "mutexGroup": {"enable": false, "mutexGroupName": "", "queueEnable": false, "timeout": 900, "timeoutVar": "900", "queue": 5}, "jobId": "job_BSQ", "matrixGroupFlag": false, "matrixControlOption": {"strategyStr": "", "includeCaseStr": "", "excludeCaseStr": "", "fastKill": true, "maxConcurrency": 5}, "nfsSwitch": true, "classType": "vmBuild"}], "id": "stage-2", "name": "编译打包阶段", "tag": ["28ee946a59f64949a74f3dee40a1bda4"], "fastKill": false, "finally": false, "stageControlOption": {"enable": true, "runCondition": "AFTER_LAST_FINISHED", "manualTrigger": false, "triggerUsers": [], "timeout": 24, "customVariables": [{"key": "param1", "value": ""}]}, "checkIn": {"manualTrigger": false, "timeout": 24, "markdownContent": false, "notifyType": ["RTX"]}, "checkOut": {"manualTrigger": false, "timeout": 24, "markdownContent": false, "notifyType": ["RTX"]}}, {"containers": [{"@type": "vmBuild", "id": "2", "name": "应用容器化部署", "elements": [{"@type": "marketBuild", "name": "获取vlan和ip", "id": "", "atomCode": "GetVlanAndIpAtomV2", "version": "1.*", "data": {"input": {"clusterName": "tscz-n-uat", "ipType": "1", "type": "app"}, "output": {"vlanAndIp": "string", "ipType": "string"}, "namespace": ""}, "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "liud", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": true, "taskAtom": ""}, {"@type": "linuxScript", "name": "设置全局变量", "id": "", "scriptType": "SHELL", "script": "# 您可以通过setEnv函数设置插件间传递的参数\n# setEnv \"FILENAME\" \"package.zip\"\n# 然后在后续的插件的表单中使用${FILENAME}引用这个变量\n\n# 您可以在质量红线中创建自定义指标，然后通过setGateValue函数设置指标值\n# setGateValue \"CodeCoverage\" $myValue\n# 然后在质量红线选择相应指标和阈值。若不满足，流水线在执行时将会被卡住\n\n# cd ${WORKSPACE} 可进入当前工作空间目录\n#获取vlan和ip\nif [ \"$ipType\" = \"auto\" ]; then\nget_vlan=$vlanAndIp\nget_ip=$ipType\nelse \nget_vlan=$(echo $vlanAndIp | cut -d'\"' -f4)\nget_ip=$(echo $vlanAndIp | cut -d'\"' -f8)\nfi\n\nsetEnv \"get_vlan\" \"${get_vlan}\"\nsetEnv \"get_ip\" \"${get_ip}\"\necho ${get_vlan} ${get_ip}", "continueNoneZero": false, "enableArchiveFile": false, "archiveFile": "", "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "version": "1.*", "classType": "linuxScript", "elementEnable": true, "atomCode": "linuxScript", "taskAtom": ""}, {"@type": "marketBuild", "name": "拉取yaml文件", "id": "", "atomCode": "GetCustomizeArchiveBkRepoCroEnv", "version": "1.*", "stepId": "_1S", "data": {"input": {"srcPaths": "https://bizdevops-repo.trinasolar.com/generic/g6ac9f/generic-public/template/deployment.yaml", "destPath": "", "credentialId": "bkrepo.global"}, "output": {}, "namespace": ""}, "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": true, "taskAtom": ""}, {"@type": "marketBuild", "name": "拉取yaml修改脚本", "id": "", "atomCode": "GetCustomizeArchiveBkRepoCroEnv", "version": "1.*", "data": {"input": {"srcPaths": "https://bizdevops-repo.trinasolar.com/generic/g6ac9f/generic-public/template/deployment_change.sh", "destPath": "", "credentialId": "bkrepo.global"}, "output": {}, "namespace": ""}, "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "liud", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": true, "taskAtom": ""}, {"@type": "linuxScript", "name": "执行yaml修改脚本", "id": "", "scriptType": "SHELL", "script": "# 您可以通过setEnv函数设置插件间传递的参数\n# setEnv \"FILENAME\" \"package.zip\"\n# 然后在后续的插件的表单中使用${FILENAME}引用这个变量\n\n# 您可以在质量红线中创建自定义指标，然后通过setGateValue函数设置指标值\n# setGateValue \"CodeCoverage\" $myValue\n# 然后在质量红线选择相应指标和阈值。若不满足，流水线在执行时将会被卡住\n\n# cd ${WORKSPACE} 可进入当前工作空间目录\n#获取vlan和ip\necho \">>>>>打印脚本内容\"\ncat deployment_change.sh\necho \"\"\necho \">>>>>结束\"\nbash deployment_change.sh", "continueNoneZero": false, "enableArchiveFile": false, "archiveFile": "", "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "version": "1.*", "classType": "linuxScript", "elementEnable": true, "atomCode": "linuxScript", "taskAtom": ""}, {"@type": "marketBuild", "name": "容器化部署", "id": "", "atomCode": "KubernetesDeploy", "version": "1.*", "data": {"input": {"certificate": "uatk8s.global", "configPath": "./${{YAML_NAME}}", "containers": "", "timeout": 600, "needToReport": true}, "output": {}, "namespace": ""}, "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": true, "taskAtom": ""}, {"@type": "singleArchive", "name": "归档deplyment.yaml", "id": "", "filePath": "./${{YAML_NAME}}", "destPath": "./${{APP_NAME}}/${{ENV}}/${{VERSION}}", "customize": true, "executeCount": 1, "version": "1.*", "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "classType": "singleArchive", "elementEnable": true, "atomCode": "singleArchive", "taskAtom": ""}, {"@type": "marketBuild", "name": "IAST-before", "id": "", "atomCode": "FlowInIASTTrinasolarBefore", "version": "1.*", "data": {"input": {"iastProjectName": "${{BK_CI_PROJECT_NAME_CN}}", "agentTag": "${{PROJ_NAME}}", "templateId": "4", "requestSwitch": true, "sensitiveSwitch": true}, "output": {}, "namespace": ""}, "additionalOptions": {"enable": false, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": false, "subscriptionPauseUser": "cwj", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": false, "taskAtom": ""}], "baseOS": "LINUX", "vmNames": [], "maxQueueMinutes": 60, "maxRunningMinutes": 900, "buildEnv": {}, "agentBind": true, "dispatchType": {"buildType": "DOCKER", "value": "bkdevops/centos7.2:v1", "imageType": "BKDEVOPS", "credentialId": "", "credentialProject": "", "imageCode": "", "imageVersion": "", "imageName": "", "performanceConfigId": 0, "imageRepositoryUserName": "", "imageRepositoryPassword": "", "imagePublicFlag": false, "imageRDType": "", "recommendFlag": true}, "showBuildResource": false, "enableExternal": false, "containerId": "2", "containerHashId": "c-5bab923e039a4323a8a72613ced08de3", "jobControlOption": {"enable": true, "prepareTimeout": 10, "timeout": 900, "timeoutVar": "900", "runCondition": "STAGE_RUNNING", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "dependOnType": "ID", "dependOnId": [], "dependOnName": "", "continueWhenFailed": false}, "mutexGroup": {"enable": false, "mutexGroupName": "", "queueEnable": false, "timeout": 900, "timeoutVar": "900", "queue": 5}, "jobId": "job_HpA", "matrixGroupFlag": false, "matrixControlOption": {"strategyStr": "", "includeCaseStr": "", "excludeCaseStr": "", "fastKill": true, "maxConcurrency": 5}, "nfsSwitch": false, "classType": "vmBuild"}, {"@type": "vmBuild", "id": "3", "name": "部署回滚", "elements": [{"@type": "marketBuild", "name": "Rancher回滚(需要时启用)", "id": "", "atomCode": "GetAppReversion", "version": "1.*", "data": {"input": {"versionId": "", "env": "${{ENV}}", "teamName": "${{TEAM_NAME}}", "projectName": "${{PROJ_NAME}}", "appName": "${{APP_NAME}}"}, "output": {"appReversion": "string"}, "namespace": "", "config": {"canPauseBeforeRun": true}}, "additionalOptions": {"enable": true, "continueWhenFailed": false, "manualSkip": false, "retryWhenFailed": false, "retryCount": 1, "manualRetry": true, "timeout": 900, "timeoutVar": "900", "runCondition": "PRE_TASK_SUCCESS", "pauseBeforeExec": true, "subscriptionPauseUser": "liud", "otherTask": "", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "enableCustomEnv": false, "customEnv": [{"key": "param1", "value": ""}], "notifyUsersOnFailed": [], "notifyTypesOnFailed": [], "notifyUserGroupsOnFailed": [], "notifyUsersOnSuccessed": [], "notifyTypesOnSuccessed": [], "notifyUserGroupsOnSuccessed": []}, "executeCount": 1, "classType": "marketBuild", "elementEnable": true, "taskAtom": ""}], "baseOS": "LINUX", "vmNames": [], "maxQueueMinutes": 60, "maxRunningMinutes": 900, "buildEnv": {}, "agentBind": true, "dispatchType": {"buildType": "DOCKER", "value": "bkdevops/centos7.2:v1", "imageType": "BKDEVOPS", "credentialId": "", "credentialProject": "", "imageCode": "", "imageVersion": "", "imageName": "", "performanceConfigId": 0, "imageRepositoryUserName": "", "imageRepositoryPassword": "", "imagePublicFlag": false, "imageRDType": "", "recommendFlag": true}, "showBuildResource": false, "enableExternal": false, "containerId": "3", "containerHashId": "c-ffb15c4d584e43ca99be472da19ce8d1", "jobControlOption": {"enable": false, "prepareTimeout": 10, "timeout": 900, "timeoutVar": "900", "runCondition": "STAGE_RUNNING", "customVariables": [{"key": "param1", "value": ""}], "customCondition": "", "dependOnType": "ID", "dependOnId": [], "dependOnName": "", "continueWhenFailed": false}, "mutexGroup": {"enable": false, "mutexGroupName": "", "queueEnable": false, "timeout": 900, "timeoutVar": "900", "queue": 5}, "jobId": "job_2Jw", "matrixGroupFlag": false, "matrixControlOption": {"strategyStr": "", "includeCaseStr": "", "excludeCaseStr": "", "fastKill": true, "maxConcurrency": 5}, "nfsSwitch": false, "classType": "vmBuild"}], "id": "stage-3", "name": "部署阶段", "tag": ["28ee946a59f64949a74f3dee40a1bda4"], "fastKill": false, "finally": false, "stageControlOption": {"enable": true, "runCondition": "AFTER_LAST_FINISHED", "manualTrigger": false, "triggerUsers": [], "timeout": 24, "customVariables": [{"key": "param1", "value": ""}]}, "checkIn": {"manualTrigger": false, "timeout": 24, "markdownContent": false, "notifyType": ["RTX"]}, "checkOut": {"manualTrigger": false, "timeout": 24, "markdownContent": false, "notifyType": ["RTX"]}}], "labels": [], "instanceFromTemplate": false, "events": {}, "staticViews": [], "latestVersion": 1}, "setting": {"projectId": "[PROJECT_ID]", "pipelineId": "", "pipelineName": "[UAT][APP_NAME]", "desc": "", "runLockType": "MULTIPLE", "successSubscription": {"types": [], "groups": [""], "users": "${BK_CI_START_USER_NAME}", "wechatGroupFlag": false, "wechatGroup": "", "wechatGroupMarkdownFlag": false, "detailFlag": false, "content": "【${BK_CI_PROJECT_NAME_CN}】- 【${BK_CI_PIPELINE_NAME}】#${BK_CI_BUILD_NUM} 执行成功，耗时${BK_CI_BUILD_TOTAL_TIME}, 触发人: ${BK_CI_START_USER_NAME}。"}, "failSubscription": {"types": [], "groups": [""], "users": "${BK_CI_START_USER_NAME}", "wechatGroupFlag": false, "wechatGroup": "", "wechatGroupMarkdownFlag": false, "detailFlag": false, "content": "【${BK_CI_PROJECT_NAME_CN}】- 【${BK_CI_PIPELINE_NAME}】#${BK_CI_BUILD_NUM} 执行失败，耗时${BK_CI_BUILD_TOTAL_TIME}, 触发人: ${BK_CI_START_USER_NAME}。"}, "labels": [], "waitQueueTimeMinute": 1, "maxQueueSize": 10, "concurrencyCancelInProgress": false, "maxPipelineResNum": 50, "maxConRunningQueueSize": 50, "version": 0, "cleanVariablesWhenRetry": false}}