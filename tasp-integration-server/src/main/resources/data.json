{"swagger": "2.0", "info": {"x-ibm-name": "mrb", "title": "Api Documentation", "version": "1.0", "description": "Api Documentation"}, "schemes": ["https"], "basePath": "/62fc833c996dc62116cf5b9a/pub-catalog", "consumes": ["application/json"], "produces": ["application/json"], "securityDefinitions": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization", "x-key-type": "client_id"}}, "x-ibm-configuration": {"assembly": {"catch": [], "execute": [{"gatewayscript": {"source": "// Copyright IBM Corp. Year 2020, 2021\nvar apim = require('apim')\nvar pathObj = apim.getvariable('request.path')\nvar remainPath = pathObj.substring(pathObj.indexOf('/',1),(pathObj.length))\nvar searchObj = apim.getvariable('request.search')\napim.setvariable('message.headers.tsltoken','123456')\napim.setvariable('remainPath',remainPath )", "title": "gatewayscript", "version": "2.0.0"}}, {"invoke": {"backend-type": "detect", "cache-response": "protocol", "cache-ttl": 900, "chunked-uploads": false, "graphql-send-type": "detect", "header-control": {"type": "blocklist", "values": []}, "http-version": "HTTP/1.1", "parameter-control": {"type": "blocklist", "values": []}, "stop-on-error": [], "target-url": "$(target-url)$(request.path)", "timeout": 60, "title": "invoke", "verb": "keep", "version": "2.2.0", "websocket-upgrade": false}}]}, "enforced": true, "testable": true, "phase": "realized", "cors": {"enabled": true}, "properties": {"target-url": {"description": "请替换缺省值为实际后端服务的真实URL", "encoded": false, "value": "http://localhost:18089"}}, "gateway": "datapower-api-gateway", "activity-log": {"enabled": true, "error-content": "payload", "success-content": "activity"}, "type": "rest"}, "paths": {"/api/upload": {"post": {"consumes": ["multipart/form-data"], "operationId": "uploadUsingPOST", "parameters": [{"description": "file", "in": "formData", "name": "file", "required": true, "type": "file"}, {"description": "taskId", "in": "query", "name": "taskId", "required": true, "type": "string"}], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/RestResponse«JSONObject»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}], "summary": "上传文件", "tags": ["api-controller"]}, "x-eco-description": {"html": "请输入"}}}, "definitions": {"DocumentSnBatch": {"properties": {"createdBy": {"type": "string"}, "createdTime": {"format": "date-time", "type": "string"}, "deletedFlag": {"format": "int32", "type": "integer"}, "documentNo": {"type": "string"}, "id": {"format": "int64", "type": "integer"}, "isComponentsInvolved": {"type": "string"}, "materialBatchNumber": {"type": "string"}, "sn": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedTime": {"format": "date-time", "type": "string"}}, "title": "DocumentSnBatch", "type": "object"}, "DocumentSnBatchDto": {"properties": {"documentNo": {"description": "单据编号", "type": "string"}, "id": {"description": "id", "format": "int64", "type": "integer"}, "isComponentsInvolved": {"description": "是否涉及组件", "type": "string"}, "materialBatchNumber": {"description": "材料批次号", "type": "string"}, "myPage": {"$ref": "#/definitions/MyPage"}, "sn": {"description": "单据编号", "type": "string"}}, "title": "DocumentSnBatchDto", "type": "object"}, "DocumentSnBatchVo": {"properties": {"createdBy": {"type": "string"}, "createdTime": {"format": "date-time", "type": "string"}, "documentNo": {"type": "string"}, "id": {"format": "int64", "type": "integer"}, "isComponentsInvolved": {"type": "string"}, "materialBatchNumber": {"type": "string"}, "sn": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedTime": {"format": "date-time", "type": "string"}}, "title": "DocumentSnBatchVo", "type": "object"}, "File": {"properties": {"absolute": {"type": "boolean"}, "absoluteFile": {"$ref": "#/definitions/File"}, "absolutePath": {"type": "string"}, "canonicalFile": {"$ref": "#/definitions/File"}, "canonicalPath": {"type": "string"}, "directory": {"type": "boolean"}, "executable": {"type": "boolean"}, "file": {"type": "boolean"}, "freeSpace": {"format": "int64", "type": "integer"}, "hidden": {"type": "boolean"}, "lastModified": {"format": "int64", "type": "integer"}, "name": {"type": "string"}, "parent": {"type": "string"}, "parentFile": {"$ref": "#/definitions/File"}, "path": {"type": "string"}, "readable": {"type": "boolean"}, "totalSpace": {"format": "int64", "type": "integer"}, "usableSpace": {"format": "int64", "type": "integer"}, "writable": {"type": "boolean"}}, "title": "File", "type": "object"}, "InputStream": {"properties": {}, "title": "InputStream", "type": "object"}, "JSONObject": {"properties": {}, "title": "JSONObject", "type": "object"}, "Map«string,object»": {"properties": {}, "title": "Map«string,object»", "type": "object"}, "MrbInfo": {"properties": {"actualComponentsNumber": {"type": "string"}, "applicationsNumber": {"format": "int32", "type": "integer"}, "approvalStatus": {"type": "string"}, "approverOfReliabilityTechnologyDepartment": {"type": "string"}, "bpmApplicant": {"type": "string"}, "causeAnalysis": {"type": "string"}, "commentsReliabilityTechnicalDepartment": {"type": "string"}, "completionDate": {"type": "string"}, "createdBy": {"type": "string"}, "createdTime": {"format": "date-time", "type": "string"}, "currentProcessingNode": {"type": "string"}, "currentProcessor": {"type": "string"}, "deletedFlag": {"format": "int32", "type": "integer"}, "descriptionAbnormalProduction": {"type": "string"}, "documentDate": {"type": "string"}, "documentNo": {"type": "string"}, "id": {"format": "int64", "type": "integer"}, "isComponentsInvolved": {"type": "string"}, "manufacturerBatchNumber": {"type": "string"}, "materialNumber": {"type": "string"}, "moduleMw": {"type": "string"}, "mrbClassify": {"type": "string"}, "mrbReportUrl": {"type": "string"}, "mrbReviewResolution": {"type": "string"}, "objectType": {"type": "string"}, "problemBrief": {"type": "string"}, "productionResults": {"type": "string"}, "responsibleManufacturer": {"type": "string"}, "responsiblePartyType": {"type": "string"}, "secondaryNav": {"type": "string"}, "sn": {"type": "string"}, "taskid": {"type": "string"}, "unit": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedTime": {"format": "date-time", "type": "string"}, "volumeBarrelNumber": {"type": "string"}, "warrantyUrl": {"type": "string"}}, "title": "MrbInfo", "type": "object"}, "MrbInfoDto": {"properties": {"actualComponentsNumber": {"type": "string"}, "applicationsNumber": {"format": "int32", "type": "integer"}, "approvalStatus": {"type": "string"}, "approverOfReliabilityTechnologyDepartment": {"type": "string"}, "bpmApplicant": {"type": "string"}, "causeAnalysis": {"type": "string"}, "commentsReliabilityTechnicalDepartment": {"type": "string"}, "completionDate": {"type": "string"}, "currentProcessingNode": {"type": "string"}, "currentProcessor": {"type": "string"}, "descriptionAbnormalProduction": {"type": "string"}, "documentDate": {"type": "string"}, "documentNo": {"type": "string"}, "id": {"format": "int64", "type": "integer"}, "isComponentsInvolved": {"type": "string"}, "manufacturerBatchNumber": {"type": "string"}, "materialNumber": {"type": "string"}, "moduleMw": {"type": "string"}, "mrbClassify": {"type": "string"}, "mrbReportUrl": {"type": "string"}, "mrbReviewResolution": {"type": "string"}, "myPage": {"$ref": "#/definitions/MyPage"}, "objectType": {"type": "string"}, "problemBrief": {"type": "string"}, "productionResults": {"type": "string"}, "responsibleManufacturer": {"type": "string"}, "responsiblePartyType": {"type": "string"}, "secondaryNav": {"type": "string"}, "sn": {"type": "string"}, "taskid": {"type": "string"}, "unit": {"type": "string"}, "volumeBarrelNumber": {"type": "string"}, "warrantyUrl": {"type": "string"}}, "title": "MrbInfoDto", "type": "object"}, "MyPage": {"properties": {"pageNum": {"format": "int32", "type": "integer"}, "pageSize": {"format": "int32", "type": "integer"}}, "title": "MyPage", "type": "object"}, "MyPageData«DocumentSnBatchVo»": {"properties": {"dataList": {"items": {"$ref": "#/definitions/DocumentSnBatchVo"}, "type": "array"}, "pageNum": {"format": "int32", "type": "integer"}, "pageSize": {"format": "int32", "type": "integer"}, "pages": {"format": "int32", "type": "integer"}, "total": {"format": "int64", "type": "integer"}}, "title": "MyPageData«DocumentSnBatchVo»", "type": "object"}, "MyPageData«mrb+csp+mes信息VO»": {"properties": {"dataList": {"items": {"$ref": "#/definitions/mrb+csp+mes信息VO"}, "type": "array"}, "pageNum": {"format": "int32", "type": "integer"}, "pageSize": {"format": "int32", "type": "integer"}, "pages": {"format": "int32", "type": "integer"}, "total": {"format": "int64", "type": "integer"}}, "title": "MyPageData«mrb+csp+mes信息VO»", "type": "object"}, "Resource": {"properties": {"description": {"type": "string"}, "file": {"$ref": "#/definitions/File"}, "filename": {"type": "string"}, "inputStream": {"$ref": "#/definitions/InputStream"}, "open": {"type": "boolean"}, "readable": {"type": "boolean"}, "uri": {"$ref": "#/definitions/URI"}, "url": {"$ref": "#/definitions/URL"}}, "title": "Resource", "type": "object"}, "RestFileExport": {"properties": {"filePath": {"type": "string"}, "userInfo": {"$ref": "#/definitions/UserInfo"}}, "title": "RestFileExport", "type": "object"}, "RestResponse«DocumentSnBatch»": {"properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/DocumentSnBatch"}, "message": {"type": "string"}, "powerType": {"type": "string"}, "success": {"type": "boolean"}}, "title": "RestResponse«DocumentSnBatch»", "type": "object"}, "RestResponse«JSONObject»": {"properties": {"code": {"type": "string"}, "data": {"properties": {}, "type": "object"}, "message": {"type": "string"}, "powerType": {"type": "string"}, "success": {"type": "boolean"}}, "title": "RestResponse«JSONObject»", "type": "object"}, "RestResponse«MrbInfo»": {"properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/MrbInfo"}, "message": {"type": "string"}, "powerType": {"type": "string"}, "success": {"type": "boolean"}}, "title": "RestResponse«MrbInfo»", "type": "object"}, "RestResponse«MyPageData«DocumentSnBatchVo»»": {"properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/MyPageData«DocumentSnBatchVo»"}, "message": {"type": "string"}, "powerType": {"type": "string"}, "success": {"type": "boolean"}}, "title": "RestResponse«MyPageData«DocumentSnBatchVo»»", "type": "object"}, "RestResponse«MyPageData«mrb+csp+mes信息VO»»": {"properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/MyPageData«mrb+csp+mes信息VO»"}, "message": {"type": "string"}, "powerType": {"type": "string"}, "success": {"type": "boolean"}}, "title": "RestResponse«MyPageData«mrb+csp+mes信息VO»»", "type": "object"}, "RestResponse«boolean»": {"properties": {"code": {"type": "string"}, "data": {"type": "boolean"}, "message": {"type": "string"}, "powerType": {"type": "string"}, "success": {"type": "boolean"}}, "title": "RestResponse«boolean»", "type": "object"}, "RestResponse«long»": {"properties": {"code": {"type": "string"}, "data": {"format": "int64", "type": "integer"}, "message": {"type": "string"}, "powerType": {"type": "string"}, "success": {"type": "boolean"}}, "title": "RestResponse«long»", "type": "object"}, "URI": {"properties": {"absolute": {"type": "boolean"}, "authority": {"type": "string"}, "fragment": {"type": "string"}, "host": {"type": "string"}, "opaque": {"type": "boolean"}, "path": {"type": "string"}, "port": {"format": "int32", "type": "integer"}, "query": {"type": "string"}, "rawAuthority": {"type": "string"}, "rawFragment": {"type": "string"}, "rawPath": {"type": "string"}, "rawQuery": {"type": "string"}, "rawSchemeSpecificPart": {"type": "string"}, "rawUserInfo": {"type": "string"}, "scheme": {"type": "string"}, "schemeSpecificPart": {"type": "string"}, "userInfo": {"type": "string"}}, "title": "URI", "type": "object"}, "URL": {"properties": {"authority": {"type": "string"}, "content": {"properties": {}, "type": "object"}, "defaultPort": {"format": "int32", "type": "integer"}, "deserializedFields": {"$ref": "#/definitions/URLStreamHandler"}, "file": {"type": "string"}, "host": {"type": "string"}, "path": {"type": "string"}, "port": {"format": "int32", "type": "integer"}, "protocol": {"type": "string"}, "query": {"type": "string"}, "ref": {"type": "string"}, "serializedHashCode": {"format": "int32", "type": "integer"}, "userInfo": {"type": "string"}}, "title": "URL", "type": "object"}, "URLStreamHandler": {"properties": {}, "title": "URLStreamHandler", "type": "object"}, "UserInfo": {"description": "用户信息", "properties": {"actualName": {"description": "用户姓名", "type": "string"}, "appname": {"description": "客户端名称", "type": "string"}, "email": {"description": "用户邮箱", "type": "string"}, "factory": {"description": "厂区", "type": "string"}, "group": {"description": "工作组", "items": {"$ref": "#/definitions/Map«string,object»"}, "type": "array"}, "isEnabled": {"description": "是否可用", "type": "string"}, "isInternal": {"description": "是否内部员工", "type": "string"}, "password": {"description": "密码", "type": "string"}, "phone": {"description": "用户手机", "type": "string"}, "remark": {"description": "备注", "type": "string"}, "role": {"description": "角色", "items": {"$ref": "#/definitions/Map«string,object»"}, "type": "array"}, "sex": {"description": "用户性别", "type": "string"}, "username": {"description": "用户账号", "type": "string"}, "workshop": {"description": "车间", "items": {"$ref": "#/definitions/Map«string,object»"}, "type": "array"}}, "title": "UserInfo", "type": "object"}, "mes信息VO": {"properties": {"defaultvalue": {"description": "厂家", "type": "string"}, "materiallotno": {"description": "材料批号", "type": "string"}, "propertyno": {"description": "厂家编号", "type": "string"}}, "title": "mes信息VO", "type": "object"}, "mrb+csp+mes信息VO": {"properties": {"actualComponentsNumber": {"description": "实际组件数量", "type": "string"}, "applicationsNumber": {"description": "申请数量", "format": "int32", "type": "integer"}, "id": {"format": "int64", "type": "integer"}, "mesInfoList": {"description": "SN对应的mes信息", "items": {"$ref": "#/definitions/mes信息VO"}, "type": "array"}, "moduleMw": {"description": "组件兆瓦数", "type": "string"}, "mrbClassify": {"description": "MRB分类;0-(可靠性、外观、功率、EL)\n     1-(可靠性、外观、EL、尺寸 材料认证、产地认证)\n     2-(电池片、玻璃、EVA、汇流条、焊带、型材、接线盒)\n     3-(可靠性、外观、EL)", "type": "string"}, "mrbReportUrl": {"description": "MRB报告地址", "type": "string"}, "responsiblePartyType": {"description": "责任方;0-天合 1-供应商", "type": "string"}, "sn": {"description": "SN(组建号)", "type": "string"}, "unit": {"description": "单位", "type": "string"}, "warrantyUrl": {"description": "质保单地址", "type": "string"}}, "title": "mrb+csp+mes信息VO", "type": "object"}}, "responses": {"200": {"description": "Successful API response.", "headers": {"X-Ratelimit-Limit": {"default": "name=eco-ratelimit10", "description": "\"name=eco-ratelimit,10\": name is the rate limit policy name and 10 means the total number of the request in current limit window", "type": "string"}, "X-Ratelimit-Remaining": {"default": "name=eco-ratelimit,5", "description": "\"name=eco-ratelimit,0\": name is the rate limit policy name and 5 means the remaining number of request is 5 in current limit window.", "type": "string"}}}, "429": {"description": "If the request overhead the rate limit number, the API will return this status code.", "headers": {"X-Ratelimit-Limit": {"default": "name=eco-ratelimit10", "description": "\"name=eco-ratelimit,10\": name is the rate limit policy name and 10 means the total number of the request in current limit window", "type": "string"}, "X-Ratelimit-Remaining": {"default": "name=eco-ratelimit,5", "description": "\"name=eco-ratelimit,0\": name is the rate limit policy name and 5 means the remaining number of request is 5 in current limit window.", "type": "string"}, "X-Ratelimit-Reset": {"default": "name=eco-ratelimit,52", "description": "\"name=eco-ratelimit,52\": name is the rate limit policy name and 52 means that 52 seconds remaining to next limit window.", "type": "string"}}}}, "tags": [{"description": "Mrb Info Controller", "name": "MRB保存BPM信息"}, {"description": "Api Controller", "name": "api-controller"}, {"description": "File Export Controller", "name": "file-export-controller"}, {"description": "Document Sn Batch Controller", "name": "单号关联"}], "security": [{"Authorization": []}], "host": "https://se-inner-common-gw.apps.ocp4stg.trinasolar.com"}