package com.trinasolar.integration.service;

import com.trinasolar.integration.constants.GitLabAccessLevel;
import com.trinasolar.integration.controller.devops.vo.GitGroupRespVO;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.GroupApi;
import org.gitlab4j.api.models.Group;
import org.gitlab4j.api.models.GroupParams;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import org.gitlab4j.api.UserApi;
import org.gitlab4j.api.models.User;

@ExtendWith(MockitoExtension.class)
class GitServiceTest {

    @Mock
    private GitLabApi gitLabApi;
    
    @Mock
    private GroupApi groupApi;
    
    @Mock
    private UserApi userApi;

    @InjectMocks
    private GitService gitService;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(gitService, "gitUrl", "https://code.trinasolar.com");
        ReflectionTestUtils.setField(gitService, "gitToken", "**************************");
    }

    /**
     * 测试当GitLab群组已存在时，应该返回现有群组
     * 验证不会调用创建群组的API
     */
    @Test
    void createGitGroup_WhenGroupExists_ShouldReturnExistingGroup() throws GitLabApiException {
        // Given
        String projectEnName = "cloud4";
        String parentGroupName = "psy";
        
//        Group parentGroup = new Group();
//      //  parentGroup.setId(3032L);
//
//        Group existingGroup = new Group();
//       // existingGroup.setId(5245L);
//        existingGroup.setName("cloud2");
//        existingGroup.setPath("cloud2");
//        existingGroup.setWebUrl("https://code.trinasolar.com/psy/cloud2");
        
//        when(gitLabApi.getGroupApi()).thenReturn(groupApi);
//        when(groupApi.getOptionalGroup("tasptest")).thenReturn(Optional.of(parentGroup));
//        when(groupApi.getOptionalGroup("tasptest/cloud2")).thenReturn(Optional.of(existingGroup));

        // When
        GitGroupRespVO result = gitService.createGitGroup(projectEnName, parentGroupName);

        // Then
        assertNotNull(result);
      //  assertEquals(5245L, result.getId());
        assertEquals("cloud4", result.getName());
        assertEquals("cloud4", result.getPath());
        assertEquals("https://code.trinasolar.com/groups/psy/cloud4", result.getWebUrl());
        
     //   verify(groupApi, never()).createGroup(any(GroupParams.class));
    }

    /**
     * 测试当GitLab群组不存在时，应该创建新的子群组
     * 验证创建群组时设置了正确的父群组ID和参数
     */
    @Test
    void createGitGroup_WhenGroupNotExists_ShouldCreateNewGroup() throws GitLabApiException {
        // Given
        String projectEnName = "new-project-test";
        String parentGroupName = "isc";
        
        Group parentGroup = new Group();
        parentGroup.setId(456L);
        
        Group newGroup = new Group();
        newGroup.setId(789L);
        newGroup.setName("new-project-test");
        newGroup.setPath("new-project-test");
        newGroup.setWebUrl("https://code.trinasolar.com/isc/new-project-test");
        
        when(gitLabApi.getGroupApi()).thenReturn(groupApi);
        when(groupApi.getOptionalGroup("isc")).thenReturn(Optional.of(parentGroup));
        when(groupApi.getOptionalGroup("isc/new-project-test")).thenReturn(Optional.empty());
        when(groupApi.createGroup(any(GroupParams.class))).thenReturn(newGroup);

        // When
        GitGroupRespVO result = gitService.createGitGroup(projectEnName, parentGroupName);

        // Then
        assertNotNull(result);
        assertEquals(789L, result.getId());
        assertEquals("new-project-test", result.getName());
        assertEquals("new-project-test", result.getPath());
        assertEquals("https://code.trinasolar.com/isc/new-project-test", result.getWebUrl());
        
//        verify(groupApi).createGroup(argThat(params ->
//            "new-project-test".equals(params.getName()) &&
//            "new-project-test".equals(params.getPath()) &&
//            Long.valueOf(456L).equals(params.getParentId())
//        ));
    }

    /**
     * 测试当没有指定父群组时，应该创建根级群组
     * 验证创建群组时父群组ID为null
     */
    @Test
    void createGitGroup_WhenNoParentGroup_ShouldCreateRootGroup() throws GitLabApiException {
        // Given
        String projectEnName = "cloud3";
        String parentGroupName = "psy";
        
        Group newGroup = new Group();
      //  newGroup.setId(999L);
        newGroup.setName("cloud3");
        newGroup.setPath("cloud3");
        newGroup.setWebUrl("https://code.trinasolar.com/psy/cloud3");
        
//        when(gitLabApi.getGroupApi()).thenReturn(groupApi);
//        when(groupApi.getOptionalGroup("null/root-project")).thenReturn(Optional.empty());
//        when(groupApi.createGroup(any(GroupParams.class))).thenReturn(newGroup);

        // When
        GitGroupRespVO result = gitService.createGitGroup(projectEnName, parentGroupName);

        // Then
        assertNotNull(result);
        assertEquals("cloud3", result.getName());
        assertEquals("https://code.trinasolar.com/groups/psy/cloud3", result.getWebUrl());

//        verify(groupApi).createGroup(argThat(params ->
//            params.getParentId() == null
//        ));
    }

    /**
     * 测试当指定的父群组不存在时，应该抛出异常
     * 验证异常类型和错误消息
     */
    @Test
    void createGitGroup_WhenParentGroupNotFound_ShouldThrowException() throws GitLabApiException {
        // Given
        String projectEnName = "test-project";
        String parentGroupName = "non-existent-parent";
        
        when(gitService.getGitLabApi()).thenReturn(gitLabApi);
        when(groupApi.getOptionalGroup("non-existent-parent")).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> gitService.createGitGroup(projectEnName, parentGroupName));
        
        assertTrue(exception.getCause() instanceof GitLabApiException);
        assertEquals("Parent group not found!", exception.getCause().getMessage());
    }

    /**
     * 测试当GitLab API调用发生异常时，应该抛出运行时异常
     * 验证异常的包装和传播
     */
    @Test
    void createGitGroup_WhenGitLabApiException_ShouldThrowRuntimeException() throws GitLabApiException {
        // Given
        String projectEnName = "test-project";
        String parentGroupName = "isc";
        
        when(gitLabApi.getGroupApi()).thenReturn(groupApi);
        when(groupApi.getOptionalGroup(anyString())).thenThrow(new GitLabApiException("API Error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> gitService.createGitGroup(projectEnName, parentGroupName));
        
        assertTrue(exception.getCause() instanceof GitLabApiException);
        assertEquals("API Error", exception.getCause().getMessage());
    }

    /**
     * 测试成功添加用户到Git群组
     * 验证当群组和用户都存在时，能够正确调用addMember方法
     */
    @Test
    void testAddUserToGitGroup_Success() throws GitLabApiException {
        // Given
        String businessDomain = "itid";
        String namespace = "test183";
        List<String> userAccounts = Arrays.asList("itoutsource.cz1510");
        GitLabAccessLevel accessLevel = GitLabAccessLevel.DEVELOPER;

//        Group group = new Group();
//        group.setId(123L);
//
//        User user1 = new User();
//        user1.setId(1L);
//        User user2 = new User();
//        user2.setId(2L);

//        when(gitLabApi.getGroupApi()).thenReturn(groupApi);
//        when(gitLabApi.getUserApi()).thenReturn(userApi);
//        when(groupApi.getOptionalGroup("psy/cloud3")).thenReturn(Optional.of(group));
//        when(userApi.getOptionalUser("itoutsource.cz1509")).thenReturn(Optional.of(user1));
//        when(userApi.getOptionalUser("itoutsource.cz1513")).thenReturn(Optional.of(user2));

        // When
        gitService.addUserToGitGroup(businessDomain, namespace, userAccounts, accessLevel);

        // Then
//        verify(groupApi).addMember(123L, 1L, accessLevel.getLevel());
//        verify(groupApi).addMember(123L, 2L, accessLevel.getLevel());
    }

    @Test
    void testAddUserToGitGroup_EmptyUserAccounts() {
        // Given
        String businessDomain = "psy";
        String namespace = "cloud3";
        List<String> userAccounts = Arrays.asList();
        GitLabAccessLevel accessLevel = GitLabAccessLevel.DEVELOPER;

        // When
        gitService.addUserToGitGroup(businessDomain, namespace, userAccounts, accessLevel);

        // Then
        verify(gitLabApi, never()).getGroupApi();
    }

    /**
     * 测试当指定的Git群组不存在时的处理
     * 验证当群组不存在时，不会调用addMember方法
     */
    @Test
    void testAddUserToGitGroup_GroupNotExists() throws GitLabApiException {
        // Given
        String businessDomain = "psy";
        String namespace = "non-existent";
        List<String> userAccounts = Arrays.asList("user1");
        GitLabAccessLevel accessLevel = GitLabAccessLevel.DEVELOPER;

        when(gitLabApi.getGroupApi()).thenReturn(groupApi);
        when(groupApi.getOptionalGroup("psy/non-existent")).thenReturn(Optional.empty());

        // When
        gitService.addUserToGitGroup(businessDomain, namespace, userAccounts, accessLevel);

        // Then
     //   verify(groupApi, never()).addMember(anyLong(), anyLong(), any());
    }

    /**
     * 测试当指定的用户不存在时的处理
     * 验证当用户不存在时，不会为该用户调用addMember方法
     */
    @Test
    void testAddUserToGitGroup_UserNotExists() throws GitLabApiException {
        // Given
        String businessDomain = "psy";
        String namespace = "cloud3";
        List<String> userAccounts = Arrays.asList("non-existent-user");
        GitLabAccessLevel accessLevel = GitLabAccessLevel.DEVELOPER;

        Group group = new Group();
        group.setId(123L);

        when(gitLabApi.getGroupApi()).thenReturn(groupApi);
        when(gitLabApi.getUserApi()).thenReturn(userApi);
        when(groupApi.getOptionalGroup("psy/cloud3")).thenReturn(Optional.of(group));
        when(userApi.getOptionalUser("non-existent-user")).thenReturn(Optional.empty());

        // When
        gitService.addUserToGitGroup(businessDomain, namespace, userAccounts, accessLevel);

        // Then
       // verify(groupApi, never()).addMember(anyLong(), anyLong(), any());
    }

    /**
     * 测试当业务域为空时的群组路径构建
     * 验证当businessDomain为null时，使用namespace作为群组路径
     */
    @Test
    void testAddUserToGitGroup_WithoutBusinessDomain() throws GitLabApiException {
        // Given
        String businessDomain = null;
        String namespace = "test-project";
        List<String> userAccounts = Arrays.asList("user1");
        GitLabAccessLevel accessLevel = GitLabAccessLevel.DEVELOPER;

        Group group = new Group();
        group.setId(123L);

        User user1 = new User();
        user1.setId(1L);

        when(gitLabApi.getGroupApi()).thenReturn(groupApi);
        when(gitLabApi.getUserApi()).thenReturn(userApi);
        when(groupApi.getOptionalGroup("test-project")).thenReturn(Optional.of(group));
        when(userApi.getOptionalUser("user1")).thenReturn(Optional.of(user1));

        // When
        gitService.addUserToGitGroup(businessDomain, namespace, userAccounts, accessLevel);

        // Then
        verify(groupApi).addMember(123L, 1L, accessLevel.getLevel());
    }

    /**
     * 测试添加用户到Git群组时过滤空用户账号
     * 验证当用户账号列表包含空字符串和null值时，只处理有效的用户账号
     */
//    @Test
//    void testAddUserToGitGroup_EmptyUserAccount() {
//        // Given
//        String businessDomain = "isc";
//        String namespace = "test-project";
//        List<String> userAccounts = Arrays.asList("user1", "", "user2", null);
//        GitLabAccessLevel accessLevel = GitLabAccessLevel.DEVELOPER;
//
//        Group group = new Group();
//        group.setId(123L);
//
//        User user1 = new User();
//        user1.setId(1L);
//        User user2 = new User();
//        user2.setId(2L);
//
//        when(gitLabApi.getGroupApi()).thenReturn(groupApi);
//        when(gitLabApi.getUserApi()).thenReturn(userApi);
//        when(groupApi.getOptionalGroup("isc/test-project")).thenReturn(Optional.of(group));
//        when(userApi.getOptionalUser("user1")).thenReturn(Optional.of(user1));
//        when(userApi.getOptionalUser("user2")).thenReturn(Optional.of(user2));
//
//        // When
//        gitService.addUserToGitGroup(businessDomain, namespace, userAccounts, accessLevel);
//
//        // Then
//        verify(groupApi).addMember(123L, 1L, accessLevel.getLevel());
//        verify(groupApi).addMember(123L, 2L, accessLevel.getLevel());
//        verify(userApi, never()).getOptionalUser("");
//        verify(userApi, never()).getOptionalUser(null);
//    }
}