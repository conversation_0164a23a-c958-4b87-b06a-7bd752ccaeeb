package com.trinasolar.integration.service.sca.impl;

import com.trinasolar.integration.api.SCAProvider;
import com.trinasolar.integration.api.entity.ApplicationProgram;
import com.trinasolar.integration.api.entity.UserApp;
import com.trinasolar.integration.service.impl.SCAServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class SCAServiceImplTest {

    @Autowired
    private SCAServiceImpl scaService;

    @Test
    void createProjects() {
        // Given
        String systemCnName = "特殊需求";
        List<UserApp> userApps=new ArrayList<>();
        UserApp userApp=new UserApp();
        userApp.setUserId(1923296746045517826L);
        userApps.add(userApp);
        UserApp userApp1=new UserApp();
        userApp1.setUserId(1923297212703780866L);
        userApps.add(userApp1);
        // When
        scaService.createProjects(systemCnName, userApps);

        // Then - 可以通过日志或其他方式验证调用结果
        System.out.println("createProjects 调用完成");//297
    }

    @Test
    void delProjects() {
        // Given

        // When
        scaService.deleteProjects(293);

        // Then - 可以通过日志或其他方式验证调用结果
        System.out.println("deleteProjects 调用完成");//294
    }


    @Test
    void createModes() {

        // Given
        ApplicationProgram program=new ApplicationProgram();
        program.setProgramNameCn("测试模块tasp8278");
        program.setGitlabRepoUrl("https://code.trinasolar.com/tasptest/devops-tes/backend62505.git");
        // When
        scaService.createModel(program,293);

        // Then - 可以通过日志或其他方式验证调用结果
        System.out.println("create modles 调用完成");//287
    }
}
