#!/bin/bash

# 创建目录
mkdir -p deps
cd deps

# 下载所有依赖包
curl -L -o iproute2.deb http://archive.ubuntu.com/ubuntu/pool/main/i/iproute2/iproute2_5.5.0-1ubuntu1_amd64.deb
curl -L -o libc6.deb http://archive.ubuntu.com/ubuntu/pool/main/g/glibc/libc6_2.31-0ubuntu9.2_amd64.deb
curl -L -o libc-bin.deb http://archive.ubuntu.com/ubuntu/pool/main/g/glibc/libc-bin_2.31-0ubuntu9.2_amd64.deb
curl -L -o libselinux1.deb http://archive.ubuntu.com/ubuntu/pool/main/libs/libselinux/libselinux1_3.0-1build2_amd64.deb
curl -L -o libpcre2-8-0.deb http://archive.ubuntu.com/ubuntu/pool/main/p/pcre2/libpcre2-8-0_10.34-7_amd64.deb
curl -L -o libelf1.deb http://archive.ubuntu.com/ubuntu/pool/main/e/elfutils/libelf1_0.176-1.1build1_amd64.deb
curl -L -o libmnl0.deb http://archive.ubuntu.com/ubuntu/pool/main/libm/libmnl/libmnl0_1.0.4-2_amd64.deb
curl -L -o libcap2.deb http://archive.ubuntu.com/ubuntu/pool/main/libc/libcap2/libcap2_1:2.32-1_amd64.deb
curl -L -o libcap-ng0.deb http://archive.ubuntu.com/ubuntu/pool/main/libc/libcap-ng/libcap-ng0_0.7.9-2.1_amd64.deb
curl -L -o libaudit1.deb http://archive.ubuntu.com/ubuntu/pool/main/a/audit/libaudit1_1:2.8.5-2ubuntu6_amd64.deb
curl -L -o libgcc-s1.deb http://archive.ubuntu.com/ubuntu/pool/main/g/gcc-10/libgcc-s1_10-20200411-0ubuntu1_amd64.deb
curl -L -o libstdc++6.deb http://archive.ubuntu.com/ubuntu/pool/main/g/gcc-10/libstdc++6_10-20200411-0ubuntu1_amd64.deb

# 下载 iproute2 二进制文件
mkdir -p iproute2
curl -L -o iproute2/ip http://archive.ubuntu.com/ubuntu/pool/main/i/iproute2/iproute2_5.5.0-1ubuntu1_amd64.deb