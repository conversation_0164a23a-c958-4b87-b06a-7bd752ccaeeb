<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.trinasolar.integration</groupId>
        <artifactId>kepler-integration</artifactId>
        <version>1.0.0</version>
    </parent>
    <groupId>com.trinasolar.integration</groupId>
    <artifactId>tasp-integration-framework</artifactId>
    <name>kepler-integration-framework</name>
    <description>kepler-integration-framework</description>

    <packaging>pom</packaging>
    <modules>
        <module>tasp-common</module>
        <module>tasp-springboot-starter-web</module>
        <module>tasp-spingboot-starter-mybatis</module>
        <module>tasp-springboot-starter-redis</module>
        <module>tasp-springboot-starter-kafka</module>
    </modules>
    <properties>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

</project>
