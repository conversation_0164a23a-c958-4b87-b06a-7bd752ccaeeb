package com.trinasolar.integration.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity 基础父类
 * created_time, creator_id, creator_name,
 * updated_time, updater_id, updater_name,
 * description, deleted
 * locked
 *
 * <AUTHOR>
 * @date 2021-03-31 09:30
 */
@Getter
@Setter
@Accessors(chain = true)
public abstract class BaseEntity extends AbstractEntity<Long> {

    private static final long serialVersionUID = -8292342948863206835L;

    @Override
    public BaseEntity setId(Long id) {
        super.setId(id);
        return this;
    }

    @Override
    public Long getId() {
        return super.getId();
    }

    /**
     * 默认记录创建时间字段，新建时自动填充
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 默认记录创建人ID字段，新建时自动填充
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 默认记录创建人姓名字段，新建时自动填充
     */
    @TableField(fill = FieldFill.INSERT)
    private String creatorName;

    /**
     * 默认记录修改时间字段，新建修改时自动填充
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 默认记录修改人ID字段，新建修改时自动填充
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updaterId;

    /**
     * 默认记录修改人姓名字段，新建修改时自动填充
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updaterName;

    /**
     * 默认逻辑删除标记，is_deleted=0有效，新建时由数据库赋值0
     */
    @TableLogic
    @JsonIgnore
    @TableField(value = "is_deleted", select = false)
    private Integer deleted;


    @TableField(value = "tenant_id")
    private Integer tenantId;

    @JsonIgnore
    protected Serializable pkVal() {
        return this.getId();
    }
}
