package com.trinasolar.integration.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

/**
 * Entity 抽象父类
 *
 * <AUTHOR>
 * @date 2021-03-31 09:25
 */
public abstract class AbstractEntity<T extends Serializable> implements Serializable {

    private static final long serialVersionUID = -5013774391952170097L;

    /**
     * 默认主键字段id，类型为Long型自增，转json时转换为String
     */
    @TableId(type = IdType.ASSIGN_ID)
    private T id;

    public AbstractEntity setId(T id) {
        this.id = id;
        return this;
    }

    public T getId() {
        return this.id;
    }

    /**
     * Entity对象转为String
     *
     * @return
     */
    @Override
    public String toString() {
        return this.getClass().getName() + ":" + this.getId();
    }
}
