package com.trinasolar.tasc.framework.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DashboardException extends RuntimeException {

    public DashboardException(String message) {
        super(message);
    }

    public DashboardException() {
        super();
    }

    public DashboardException(String message, Throwable cause) {
        super(message, cause);
    }

    public DashboardException(Throwable cause) {
        super(cause);
    }

    protected DashboardException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
