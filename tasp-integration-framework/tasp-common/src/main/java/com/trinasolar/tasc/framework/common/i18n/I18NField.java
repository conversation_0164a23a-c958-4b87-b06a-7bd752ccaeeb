package com.trinasolar.tasc.framework.common.i18n;


import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

@Documented
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.FIELD, ElementType.ANNOTATION_TYPE})
public @interface I18NField {
    /**
     * 与I18NClass注解配合使用，在数据库对象DO类上标记之后，
     * 该类中的属性字段进行I18NField的扫描，如有字段被标记，则执行翻译表的翻译
     * 翻译逻辑：
     */
}
