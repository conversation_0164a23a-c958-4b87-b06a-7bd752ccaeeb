package com.trinasolar.tasc.framework.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiMarketException extends RuntimeException {

    public ApiMarketException(String message) {
        super(message);
    }

    public ApiMarketException() {
        super();
    }

    public ApiMarketException(String message, Throwable cause) {
        super(message, cause);
    }

    public ApiMarketException(Throwable cause) {
        super(cause);
    }

    protected ApiMarketException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
