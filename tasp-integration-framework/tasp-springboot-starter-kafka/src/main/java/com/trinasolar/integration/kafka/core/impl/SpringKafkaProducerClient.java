package com.trinasolar.integration.kafka.core.impl;

import com.trinasolar.integration.kafka.core.KafkaProducerClient;
import org.springframework.kafka.core.KafkaTemplate;

public class SpringKafkaProducerClient implements KafkaProducerClient {

    private final KafkaTemplate<String, String> kafkaTemplate;

    public SpringKafkaProducerClient(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    @Override
    public void send(String topic, String key, String value) {
        kafkaTemplate.send(topic, key, value);
    }
}


