package com.trinasolar.integration.kafka.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = TsKafkaProperties.PREFIX)
public class TsKafkaProperties {
    public static final String PREFIX = "trinasolar.kafka";

    /**
     * 是否自动创建 topic
     */
    private boolean createTopicEnabled = false;

    /**
     * 默认 topic 名称
     */
    private String topicName = "datashare-app-system";

    /**
     * 分区数
     */
    private int partitions = 3;

    /**
     * 副本数
     */
    private short replicas = 1;

    public boolean isCreateTopicEnabled() {
        return createTopicEnabled;
    }

    public void setCreateTopicEnabled(boolean createTopicEnabled) {
        this.createTopicEnabled = createTopicEnabled;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public int getPartitions() {
        return partitions;
    }

    public void setPartitions(int partitions) {
        this.partitions = partitions;
    }

    public short getReplicas() {
        return replicas;
    }

    public void setReplicas(short replicas) {
        this.replicas = replicas;
    }
}


