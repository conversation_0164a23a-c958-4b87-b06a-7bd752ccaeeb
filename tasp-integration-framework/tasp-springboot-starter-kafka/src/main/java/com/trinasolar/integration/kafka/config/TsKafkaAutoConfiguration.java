package com.trinasolar.integration.kafka.config;

import org.apache.kafka.clients.admin.NewTopic;
import com.trinasolar.integration.kafka.core.KafkaProducerClient;
import com.trinasolar.integration.kafka.core.impl.SpringKafkaProducerClient;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.core.KafkaTemplate;

/**
 * Kafka 自动配置
 */
@AutoConfiguration(after = KafkaAutoConfiguration.class)
@EnableConfigurationProperties(TsKafkaProperties.class)
public class TsKafkaAutoConfiguration {

    @Bean
    @ConditionalOnProperty(prefix = TsKafkaProperties.PREFIX, name = "create-topic-enabled", havingValue = "true")
    @ConditionalOnMissingBean
    @ConditionalOnBean(KafkaTemplate.class)
    public NewTopic defaultTopic(TsKafkaProperties properties) {
        String name = properties.getTopicName();
        int partitions = properties.getPartitions();
        short replicas = properties.getReplicas();
        return new NewTopic(name, partitions, replicas);
    }

    @Bean
    @ConditionalOnBean(KafkaTemplate.class)
    @ConditionalOnMissingBean(KafkaProducerClient.class)
    public KafkaProducerClient kafkaProducerClient(KafkaTemplate<String, String> kafkaTemplate) {
        return new SpringKafkaProducerClient(kafkaTemplate);
    }
}


