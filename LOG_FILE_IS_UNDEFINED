2025-09-03 10:15:24.187 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-03 10:15:24.346 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-03 10:15:24.348 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:15:24.349 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:15:24.349 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:15:24.350 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:15:24.350 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:15:24.350 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:15:24.350 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:15:24.437 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-03 10:15:24.446 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-03 10:15:24.447 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-03 10:15:25.019 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-03 10:15:25.049 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-03 10:15:25.076 | [31m WARN 95563[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-03 10:15:25.095 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-03 10:15:25.119 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-03 10:15:25.120 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-03 10:15:26.359 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 10:15:26.364 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 10:15:26.431 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 46 ms. Found 0 Redis repository interfaces.
2025-09-03 10:15:26.718 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=3e7ea3de-5654-3304-a6d5-ac101cfeae2e
2025-09-03 10:15:26.774 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-03 10:15:26.787 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 10:15:26.787 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 10:15:26.788 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 10:15:26.788 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-03 10:15:26.788 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:15:26.788 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:15:26.788 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:15:26.788 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:15:26.788 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:15:26.788 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:15:26.788 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:15:26.789 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:15:26.798 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-03 10:15:26.806 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-03 10:15:26.806 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-03 10:15:26.956 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 10:15:26.958 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 10:15:26.959 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$658/0x00000008005a1c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 10:15:26.960 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 10:15:27.357 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-03 10:15:27.367 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-03 10:15:27.368 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-03 10:15:27.512 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-03 10:15:27.513 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2374 ms
2025-09-03 10:15:27.797 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-03 10:15:27.866 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-03 10:15:28.309 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-03 10:15:28.310 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-03 10:15:28.310 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-03 10:15:28.358 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-03 10:15:30.409 | [31m WARN 95563[0;39m | [1;33mmain[0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataShareServiceImpl': Unsatisfied dependency expressed through field 'dictClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.trinasolar.integration.api.RemoteDictProvider': Unexpected exception during bean creation; nested exception is java.lang.NullPointerException: name
2025-09-03 10:15:30.412 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-03 10:15:30.414 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-03 10:15:30.421 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-03 10:15:30.422 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-03 10:15:30.422 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-03 10:15:30.423 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Stopping service [Tomcat]
2025-09-03 10:15:30.438 | [34m INFO 95563[0;39m | [1;33mmain[0;39m [1;32mConditionEvaluationReportLoggingListener[0;39m | 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-09-03 10:15:30.475 | [1;31mERROR 95563[0;39m | [1;33mmain[0;39m [1;32mo.s.boot.SpringApplication              [0;39m | Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataShareServiceImpl': Unsatisfied dependency expressed through field 'dictClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.trinasolar.integration.api.RemoteDictProvider': Unexpected exception during bean creation; nested exception is java.lang.NullPointerException: name
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.trinasolar.integration.KeplerIntegrationServerApplication.main(KeplerIntegrationServerApplication.java:20)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.trinasolar.integration.api.RemoteDictProvider': Unexpected exception during bean creation; nested exception is java.lang.NullPointerException: name
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 20 common frames omitted
Caused by: java.lang.NullPointerException: name
	at feign.Util.checkNotNull(Util.java:123)
	at feign.Target$HardCodedTarget.<init>(Target.java:78)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getTarget(FeignClientFactoryBean.java:451)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getObject(FeignClientFactoryBean.java:402)
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.lambda$registerFeignClient$0(FeignClientsRegistrar.java:235)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.obtainFromSupplier(AbstractAutowireCapableBeanFactory.java:1249)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	... 28 common frames omitted

2025-09-03 10:15:30.476 | [31m WARN 95563[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-03 10:16:56.427 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-03 10:16:56.487 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-03 10:16:56.488 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:16:56.488 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:16:56.488 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:16:56.488 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:16:56.488 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:16:56.489 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:16:56.489 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:16:56.534 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-03 10:16:56.539 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-03 10:16:56.540 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-03 10:16:56.978 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-03 10:16:57.003 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-03 10:16:57.061 | [31m WARN 96169[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-03 10:16:57.074 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-03 10:16:57.099 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-03 10:16:57.100 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-03 10:16:58.122 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 10:16:58.124 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 10:16:58.176 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-09-03 10:16:58.460 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=3e7ea3de-5654-3304-a6d5-ac101cfeae2e
2025-09-03 10:16:58.509 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-03 10:16:58.523 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 10:16:58.523 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 10:16:58.523 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 10:16:58.523 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-03 10:16:58.523 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:16:58.523 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:16:58.523 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:16:58.524 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:16:58.524 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:16:58.524 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 10:16:58.524 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:16:58.524 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 10:16:58.531 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-03 10:16:58.539 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-03 10:16:58.539 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-03 10:16:58.666 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 10:16:58.668 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 10:16:58.669 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$658/0x00000008005a1c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 10:16:58.671 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 10:16:59.101 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-03 10:16:59.109 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-03 10:16:59.110 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-03 10:16:59.230 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-03 10:16:59.231 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2109 ms
2025-09-03 10:16:59.563 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-03 10:16:59.627 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-03 10:17:00.036 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-03 10:17:00.037 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-03 10:17:00.037 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-03 10:17:00.079 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-03 10:17:02.113 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-03 10:17:02.121 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-03 10:17:02.121 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-03 10:17:02.121 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-03 10:17:02.121 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-03 10:17:02.121 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-03 10:17:02.121 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-03 10:17:02.122 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-03 10:17:02.123 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-03 10:17:02.402 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-03 10:17:02.481 | [31m WARN 96169[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-03 10:17:02.736 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756865822000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756865821000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756865822623 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756865819000 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756865821617 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756865820000 3 connected

2025-09-03 10:17:02.799 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-03 10:17:02.799 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-03 10:17:02.831 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-03 10:17:03.284 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-03 10:17:03.315 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-2[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-03 10:17:03.320 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-29[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-03 10:17:03.353 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-8[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-03 10:17:03.553 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-17[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-03 10:17:03.602 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-2[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-03 10:17:03.822 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-27[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-03 10:17:03.823 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-27[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-03 10:17:03.823 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-27[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-03 10:17:03.858 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-03 10:17:03.858 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-03 10:17:03.858 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-03 10:17:04.078 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-03 10:17:04.079 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-03 10:17:04.079 | [34m INFO 96169[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-03 10:17:05.079 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-03 10:17:05.480 | [31m WARN 96169[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-03 10:17:05.616 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-03 10:17:05.617 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-03 10:17:05.617 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-03 10:17:05.843 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 10:17:05.849 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 10:17:05.865 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-03 10:17:05.868 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-03 10:17:05.869 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-03 10:17:05.878 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-03 10:17:05.892 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 10.507 seconds (JVM running for 11.253)
2025-09-03 10:17:05.905 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-***********_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-03 10:17:05.906 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-***********_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-03 10:17:05.906 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-***********_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-03 10:17:05.906 | [34m INFO 96169[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-***********_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-03 10:17:06.320 | [34m INFO 96169[0;39m | [1;33mcom.alibaba.nacos.naming.push.receiver[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | received push data: {"type":"dom","data":"{\"name\":\"DEFAULT_GROUP@@kepler-integration\",\"clusters\":\"DEFAULT\",\"cacheMillis\":10000,\"hosts\":[{\"instanceId\":\"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration\",\"ip\":\"************\",\"port\":80,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"DEFAULT_GROUP@@kepler-integration\",\"metadata\":{\"preserved.heart.beat.timeout\":\"15000\",\"preserved.ip.delete.timeout\":\"30000\",\"preserved.register.source\":\"SPRING_CLOUD\",\"preserved.heart.beat.interval\":\"5000\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000},{\"instanceId\":\"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration\",\"ip\":\"************\",\"port\":80,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"DEFAULT_GROUP@@kepler-integration\",\"metadata\":{\"preserved.heart.beat.timeout\":\"15000\",\"preserved.ip.delete.timeout\":\"30000\",\"preserved.register.source\":\"SPRING_CLOUD\",\"preserved.heart.beat.interval\":\"5000\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000}],\"lastRefTime\":1756865826438,\"checksum\":\"\",\"allIPs\":false,\"reachProtectionThreshold\":false,\"valid\":true}","lastRefTime":22350983695863504} from /***********
2025-09-03 10:17:06.323 | [34m INFO 96169[0;39m | [1;33mcom.alibaba.nacos.naming.push.receiver[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 10:17:06.323 | [34m INFO 96169[0;39m | [1;33mcom.alibaba.nacos.naming.push.receiver[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 10:17:11.961 | [31m WARN 96169[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-03 10:17:11.961 | [31m WARN 96169[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-03 10:17:11.961 | [31m WARN 96169[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-03 10:17:11.961 | [31m WARN 96169[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-03 10:17:11.982 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-03 10:17:11.983 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-03 10:17:11.983 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-03 10:17:11.997 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-03 10:17:11.997 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-03 10:17:12.611 | [34m INFO 96169[0;39m | [1;33mcom.alibaba.nacos.naming.push.receiver[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | received push data: {"type":"dom","data":"{\"name\":\"DEFAULT_GROUP@@kepler-integration\",\"clusters\":\"DEFAULT\",\"cacheMillis\":10000,\"hosts\":[{\"instanceId\":\"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration\",\"ip\":\"************\",\"port\":80,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"DEFAULT_GROUP@@kepler-integration\",\"metadata\":{\"preserved.heart.beat.timeout\":\"15000\",\"preserved.ip.delete.timeout\":\"30000\",\"preserved.register.source\":\"SPRING_CLOUD\",\"preserved.heart.beat.interval\":\"5000\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000}],\"lastRefTime\":1756865832642,\"checksum\":\"\",\"allIPs\":false,\"reachProtectionThreshold\":false,\"valid\":true}","lastRefTime":22350989900143929} from /***********
2025-09-03 10:17:12.612 | [34m INFO 96169[0;39m | [1;33mcom.alibaba.nacos.naming.push.receiver[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 10:17:12.613 | [34m INFO 96169[0;39m | [1;33mcom.alibaba.nacos.naming.push.receiver[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 10:17:15.007 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-03 10:17:15.007 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-03 10:17:16.401 | [34m INFO 96169[0;39m | [1;33mcom.alibaba.nacos.naming.push.receiver[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | received push data: {"type":"dom","data":"{\"name\":\"DEFAULT_GROUP@@kepler-integration\",\"clusters\":\"DEFAULT\",\"cacheMillis\":10000,\"hosts\":[{\"instanceId\":\"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration\",\"ip\":\"************\",\"port\":80,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"DEFAULT_GROUP@@kepler-integration\",\"metadata\":{\"preserved.heart.beat.timeout\":\"15000\",\"preserved.ip.delete.timeout\":\"30000\",\"preserved.register.source\":\"SPRING_CLOUD\",\"preserved.heart.beat.interval\":\"5000\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000},{\"instanceId\":\"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration\",\"ip\":\"************\",\"port\":80,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"DEFAULT_GROUP@@kepler-integration\",\"metadata\":{\"preserved.heart.beat.timeout\":\"15000\",\"preserved.ip.delete.timeout\":\"30000\",\"preserved.register.source\":\"SPRING_CLOUD\",\"preserved.heart.beat.interval\":\"5000\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000}],\"lastRefTime\":1756865826438,\"checksum\":\"\",\"allIPs\":false,\"reachProtectionThreshold\":false,\"valid\":true}","lastRefTime":22350983695863504} from /***********
2025-09-03 10:17:16.401 | [31m WARN 96169[0;39m | [1;33mcom.alibaba.nacos.naming.push.receiver[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | out of date data received, old-t: 1756865832642, new-t: 1756865826438
2025-09-03 10:17:16.402 | [34m INFO 96169[0;39m | [1;33mcom.alibaba.nacos.naming.push.receiver[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 10:17:16.402 | [34m INFO 96169[0;39m | [1;33mcom.alibaba.nacos.naming.push.receiver[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 10:17:16.881 | [34m INFO 96169[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 10:17:16.883 | [34m INFO 96169[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-03 10:17:16.883 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-03 10:17:19.893 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-03 10:17:19.893 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-03 10:17:19.893 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-03 10:17:19.893 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-03 10:17:19.893 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-03 10:17:19.893 | [31m WARN 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-03 10:17:19.893 | [31m WARN 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-03 10:17:19.893 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-03 10:17:19.894 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-03 10:17:19.894 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-03 10:17:19.894 | [31m WARN 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-03 10:17:19.926 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-03 10:17:19.928 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-03 10:17:19.935 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-03 10:17:19.935 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-03 10:17:19.935 | [34m INFO 96169[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
